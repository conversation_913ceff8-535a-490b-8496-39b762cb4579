<template>
  <div class="page-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="page-header">
          <h2 class="page-title">编辑商品</h2>
          <div class="page-actions">
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
              保存修改
            </el-button>
          </div>
        </div>
      </template>

      <div class="page-content">
        <el-form
          label-position="top"
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          class="product-form"
          @submit.prevent
          v-loading="loading"
        >
          <!-- 基本信息 -->
          <div class="form-section">
            <h3 class="section-title">基本信息</h3>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="商品名称" prop="name">
                  <el-input v-model="form.name" placeholder="请输入商品名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="商品分类" prop="productCategoryId">
                  <el-select
                    v-model="form.category_id"
                    placeholder="请选择商品分类"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="category in thirdLevelCategories"
                      :key="category.id"
                      :label="category.fullName"
                      :value="category.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="商品编码" prop="out_product_id">
                  <el-input v-model="form.out_product_id" placeholder="商品编码" disabled>
                    <template #append>
                      <el-tooltip content="商品编码是系统生成的唯一标识" placement="top">
                        <el-icon><InfoFilled /></el-icon>
                      </el-tooltip>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="品牌" prop="brand_name">
                  <el-input v-model="form.brand_name" placeholder="请输入品牌名称" clearable />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="商品排序" prop="sort">
                  <el-input-number v-model="form.sort" :min="0" style="width: 100%" />
                  <div class="field-tip">
                    <el-icon><InfoFilled /></el-icon>
                    <span>排序值越小越靠前，用于控制商品在列表中的显示顺序</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="上架状态" prop="publish_status">
                  <el-radio-group v-model="form.publish_status">
                    <el-radio :label="1">上架</el-radio>
                    <el-radio :label="0">下架</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 商品图片 -->
          <div class="form-section">
            <h3 class="section-title">商品图片</h3>
            <el-form-item label="商品主图" prop="pic">
              <el-upload
                class="main-image-uploader"
                action="#"
                :auto-upload="false"
                :show-file-list="false"
                :on-change="handleMainImageChange"
              >
                <img v-if="form.pic" :src="form.pic" class="main-image" />
                <div v-else class="upload-placeholder">
                  <el-icon><Plus /></el-icon>
                  <div class="upload-text">上传主图</div>
                </div>
              </el-upload>
              <div class="upload-tip">建议尺寸：800x800px，支持jpg、png格式</div>
            </el-form-item>

            <el-form-item label="商品相册" prop="albumPics">
              <el-upload
                class="gallery-uploader"
                action="#"
                :auto-upload="false"
                list-type="picture-card"
                :on-change="handleGalleryChange"
                :on-remove="handleGalleryRemove"
                :file-list="galleryList"
                multiple
                :limit="8"
              >
                <el-icon><Plus /></el-icon>
              </el-upload>
              <div class="upload-tip">请上传8张商品展示图，建议尺寸：800x800px</div>
            </el-form-item>
          </div>
          <div class="form-section">
            <h3 class="section-title">商品包裹信息</h3>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="商品包裹重量">
                  <div class="weight-input-group">
                    <el-select v-model="weightUnit" style="width: 110px" placeholder="单位">
                      <el-option label="g" value="g" />
                      <el-option label="kg" value="kg" />
                      <el-option label="lb" value="lb" />
                      <el-option label="oz" value="oz" />
                    </el-select>
                    <el-input
                    v-if="form.weight>0"
                      style="width: 100%"
                      v-model="form.weight"
                      placeholder="请输入重量"
                    >
                    </el-input>
                    <el-input
                    v-else
                      style="width: 100%"
                      v-model="form.package.weight"
                      placeholder="请输入重量"
                    >
                    </el-input>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item v-if="form.weight>0" label="商品包裹长度">
                  <el-input v-model="form.package.length" placeholder="请输入商品包裹长度">
                    <template #append>cm</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item v-if="form.weight>0" label="商品包裹宽度">
                  <el-input v-model="form.package.width" placeholder="请输入商品包裹宽度">
                    <template #append>cm</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item v-if="form.weight>0" label="商品包裹高度">
                  <el-input v-model="form.package.height" placeholder="请输入商品包裹高度">
                    <template #append>cm</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 商品属性 -->
          <!-- 商品销售信息部分 -->
          <div class="form-section">
            <h3 class="section-title">商品销售信息</h3>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="是否开启商品变体">
                  <el-switch v-model="form.enableVariant" @change="handleVariantSwitchChange" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="是否是衣装类商品">
                  <el-switch v-model="form.isclothing" @change="handleClothingSwitchChange" />
                </el-form-item>
              </el-col>
            </el-row>
            <!-- 变体属性类型管理（仅在开启变体时显示） -->
            <div v-if="form.enableVariant" class="variant-attributes">
              <div class="attributes-header">
                <h4>变体属性类型</h4>
                <el-button
                  type="primary"
                  size="small"
                  @click="showAttributeInput = true"
                  v-if="!showAttributeInput"
                >
                  <el-icon><Plus /></el-icon>
                  添加属性类型
                </el-button>
              </div>

              <!-- 添加属性类型输入框 -->
              <div v-if="showAttributeInput" class="attribute-input">
                <el-input
                  v-model="newAttribute"
                  placeholder="输入属性类型名称（如：颜色、材质等）"
                  style="width: 300px; margin-right: 10px"
                />
                <el-button type="primary" size="small" @click="addAttributeType"> 确认 </el-button>
                <el-button size="small" @click="showAttributeInput = false"> 取消 </el-button>
              </div>

              <!-- 已添加的属性类型列表 -->
              <div class="attribute-types">
                <el-tag
                  v-for="(attr, index) in form.variantAttributes"
                  :key="index"
                  closable
                  @close="removeAttributeType(index)"
                >
                  {{ attr }}
                </el-tag>
              </div>

              <!-- 属性为空提示 -->
              <div
                v-if="form.variantAttributes.length === 0 && !showAttributeInput"
                class="no-attributes-tip"
              >
                <el-icon><InfoFilled /></el-icon>
                <span>暂无属性类型，请点击上方按钮添加</span>
              </div>
            </div>
            <div v-if="form.isclothing" class="variant-attributes">
              <div class="attributes-header">
                <h4>商品尺寸管理</h4>
                <el-button
                  type="primary"
                  size="small"
                  @click="showSizeInput = true"
                  v-if="!showSizeInput"
                >
                  <el-icon><Plus /></el-icon>
                  添加尺寸类型
                </el-button>
              </div>

              <!-- 添加尺寸输入框（与属性类型输入框结构一致） -->
              <div v-if="showSizeInput" class="attribute-input">
                <el-input
                  v-model="newSize"
                  placeholder="输入尺寸类型（如：S/M/L/XL等）"
                  style="width: 300px; margin-right: 10px"
                />
                <el-button type="primary" size="small" @click="addSize">确认</el-button>
                <el-button size="small" @click="showSizeInput = false">取消</el-button>
              </div>

              <!-- 已添加的尺寸类型列表（与属性类型标签区一致） -->
              <div class="attribute-types">
                <el-tag
                  v-for="(size, index) in availableSizes"
                  :key="index"
                  closable
                  @close="removeSize(index)"
                >
                  {{ size }}
                </el-tag>
              </div>

              <!-- 尺寸为空提示（与属性类型提示一致） -->
              <div v-if="availableSizes.length === 0 && !showSizeInput" class="no-attributes-tip">
                <el-icon><InfoFilled /></el-icon>
                <span>暂无尺寸类型，请点击上方按钮添加</span>
              </div>
            </div>
            <el-button @click="console.log(form)">查看</el-button>
            <el-table :data="form.productVariant" border style="width: 100%">
              <!-- 序号列 -->
              <el-table-column label="序号" width="60">
                <template #default="{ row }">
                  {{ row.variantId }}
                </template>
              </el-table-column>

              <!-- 商品ID列 -->
              <el-table-column label="商品ID" width="100">
                <template #default>
                  <span class="auto-generated-id">自动生成</span>
                </template>
              </el-table-column>
              <!-- 动态属性列 -->
              <el-table-column
                v-for="(attr, index) in form.variantAttributes"
                v-if="form.enableVariant"
                :key="index"
                :label="attr"
                width="130"
              >
                <template #default="{ row }">
                  <el-input
                    v-model="row.attributes[attr]"
                    :placeholder="`请输入${attr}`"
                    clearable
                    @change="handleAttributesChange(row, index)"
                  />
                </template>
              </el-table-column>

              <!-- 价格列（可编辑） -->
              <el-table-column label="价格($)" width="150">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.price"
                    :min="0"
                    :precision="2"
                    controls-position="right"
                    placeholder="请输入价格"
                  />
                </template>
              </el-table-column>

              <!-- 库存列（可编辑） -->
              <el-table-column label="当前库存" width="150">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.inventory"
                    :min="0"
                    :step="1"
                    controls-position="right"
                    style="width: 100%"
                    placeholder="请输入库存"
                  />
                </template>
              </el-table-column>

              <el-table-column label="商品图片" width="120">
                <template #default="{ row }">
                  <el-upload
                    class="variant-image-uploader"
                    action="#"
                    :auto-upload="false"
                    :show-file-list="false"
                    :on-change="(file) => handleVariantImageChange(file, row)"
                  >
                    <img v-if="row.image" :src="row.image" class="variant-image" />
                    <div v-else class="upload-placeholder">
                      <el-icon><Plus /></el-icon>
                    </div>
                  </el-upload>
                </template>
              </el-table-column>

              <!-- Seller SKU列（可编辑） -->
              <el-table-column label="Seller SKU(可选)">
                <template #default="{ row }">
                  <el-input v-model="row.sellerSKU" placeholder="请输入SKU" clearable />
                </template>
              </el-table-column>

              <!-- 操作列（仅在开启变体时显示） -->
              <el-table-column v-if="form.enableVariant" label="操作" width="100">
                <template #default="{ $index }">
                  <el-button type="danger" size="small" circle @click="removeVariantRow($index)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 添加行按钮（仅在开启变体时显示） -->
            <div v-if="form.enableVariant" class="variant-actions">
              <el-button type="primary" size="small" @click="addVariantRow">
                <el-icon><Plus /></el-icon>
                添加变体
              </el-button>
            </div>
          </div>

          <div class="form-section">
            <h3 class="section-title">商品详情</h3>
            <el-form-item label="商品详情" prop="detail_html">
              <div class="editor">
                <QuillEditor
                  v-model:content="form.detail_html"
                  :options="editorOptions"
                  contentType="html"
                  style="height: 300px; width: auto"
                />
              </div>
            </el-form-item>
            <el-form-item label="图文介绍" prop="graphic_introduction">
              <el-upload
                list-type="picture-card"
                action="#"
                :auto-upload="false"
                :on-change="handleIntroductionChange"
                :on-remove="handleIntroductionRemove"
                multiple
                :file-list="graphicIntroductList"
              >
                <el-icon><Plus /></el-icon>
              </el-upload>
              <div class="upload-tip">建议尺寸：800x800px，支持jpg、png格式</div>
            </el-form-item>
            <el-form-item label="商品文档(PDF)" prop="pdf_document">
              <el-upload
                class="pdf-uploader"
                action="#"
                :auto-upload="false"
                :show-file-list="true"
                :on-change="handlePdfChange"
                :on-remove="handlePdfRemove"
                :limit="3"
                :file-list="pdfFileList"
                :before-upload="beforePdfUpload"
                accept=".pdf,application/pdf"
              >
                <el-button type="primary" plain>
                  <el-icon><Document /></el-icon>
                  <span>上传PDF文档</span>
                </el-button>
                <template #tip>
                  <div class="upload-tip">仅支持PDF格式，最大10MB</div>
                  <!-- 添加总大小提示 -->
                  <div class="upload-size-info" v-if="pdfFile">
                    <span
                      class="size-text"
                      :class="{
                        warning: pdfFile.size / 1024 / 1024 > 7 && pdfFile.size / 1024 / 1024 <= 10,
                        danger: pdfFile.size / 1024 / 1024 > 10,
                        normal: pdfFile.size / 1024 / 1024 <= 7,
                      }"
                    >
                      PDF大小：{{ (pdfFile.size / 1024 / 1024).toFixed(2) }}MB / 10MB
                    </span>
                    <el-progress
                      class="size-progress"
                      :percentage="(pdfFile.size / 1024 / 1024) * 10"
                      :status="
                        pdfFile.size / 1024 / 1024 > 10
                          ? 'exception'
                          : pdfFile.size / 1024 / 1024 > 7
                            ? 'warning'
                            : ''
                      "
                      :stroke-width="8"
                    ></el-progress>
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, onMounted, onBeforeMount } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus, Edit, Delete, InfoFilled } from '@element-plus/icons-vue'
import type { UploadUserFile } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { getProductDetail, getSellerProductDetail, updateSellerProduct } from '@/api/product'
import { getCategoryTree } from '@/api/category'
import { Quill, QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
// 定义API响应接口
interface ApiResponse<T = any> {
  code: number
  msg: string | null
  data: T | null
}
const productName = ref('')
const router = useRouter()
const route = useRoute()
const formRef = ref()
const submitLoading = ref(false)
const loading = ref(true)
const galleryList = ref<UploadUserFile[]>([])
const graphicIntroductList = ref<UploadUserFile[]>([])
// 获取商品ID
const productId = ref<number>(Number(route.params.id))

// 分类数据
const categoryOptions = ref<any[]>([])
const thirdLevelCategories = ref<{ id: number; fullName: string }[]>([])

// 品牌选项
const brandOptions = [
  { id: 1, name: '华为' },
  { id: 2, name: '小米' },
  { id: 3, name: '苹果' },
  { id: 4, name: '三星' },
  { id: 5, name: 'OPPO' },
  { id: 6, name: 'vivo' },
]

// 单位选项
const unitOptions = ['件', '个', '套', '盒', '袋', '箱', '瓶', '千克', '克', '米', '厘米']

// 预定义的销售属性
const predefinedAttrs = [
  {
    groupName: '基本属性',
    attrs: ['颜色', '尺寸', '材质', '款式', '重量'],
  },
  {
    groupName: '电子产品',
    attrs: ['内存', '存储容量', '处理器', '屏幕尺寸', '电池容量', '操作系统'],
  },
  {
    groupName: '服装',
    attrs: ['季节', '适用人群', '领型', '袖长', '风格', '版型'],
  },
  {
    groupName: '食品',
    attrs: ['口味', '产地', '保质期', '包装', '净含量'],
  },
] as { groupName: string; attrs: string[] }[]

// 包裹重量单位
const weightUnit = ref('g')
const editorOptions = reactive({
  modules: {
    toolbar: [
      // 第一行：文本样式
      [{ header: [1, 2, 3, false] }], // 标题
      ['bold', 'italic', 'underline', 'strike'], // 加粗/斜体/下划线/删除线
      [{ color: [] }, { background: [] }], // 文字颜色/背景色

      // 第二行：段落格式
      [{ list: 'ordered' }, { list: 'bullet' }], // 列表
      [{ indent: '-1' }, { indent: '+1' }], // 缩进
      [{ align: [] }], // 对齐方式

      // 第三行：其他功能
      ['blockquote', 'code-block'], // 引用/代码块
      ['link'], // 链接（保留基础链接功能）
      ['clean'], // 清除格式
    ],
  },
  theme: 'snow',
})
const showAttributeInput = ref(false)
const newAttribute = ref('')
// 尺寸管理相关状态
const newSize = ref('')
const showSizeInput = ref(false)
const availableSizes = ref<string[]>([])
// 表单数据
const form = reactive({
  id: null,
  startProductVariant: false,
  product_snapshot_id: null,
  brand_id: null,
  category_id: null,
  out_product_id: '',
  name: '',
  pic: '',
  album_pics: '',
  weight:0,
  publish_status: 1,
  package: {
    weight: null,
    height: null,
    width: null,
    length: null,
  },
  variantAttributes: [] as string[],
  enableVariant: false,
  isclothing: false,
  productVariant: [
    {
      image: null,
      variantPic: null,
      variantId: 1, // 变体ID，前端自增
      productUPC: '', // 商品UPC码（可选）
      price: null, // 价格
      inventory: null, // 库存
      sellerSKU: '', // 卖家SKU
      productAttr: '',
      attributes: {} as Record<string, string>, // 动态存储属性值
      size: [] as string[], // 尺寸（可选）
    },
  ],
  sort: 0,
  price: 0,
  unit: '件',
  detail_html: '',
  brand_name: '',
  product_category_name: '',
  introduce_pics: '',
  product_pdf: '',
})

// 在表单数据中添加存储原始文件的字段
const mainPicFile = ref<File | null>(null)
const albumPicFiles = ref<File[]>([])
const graphicIntroductFiles = ref<File[]>([])
const pdfFile = ref<File | null>(null)
const pdfFiles = ref<File[]>([]); // 存储原始文件对象
const pdfFileList = ref<UploadUserFile[]>([]); // 存储上传列表中的文件
// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' },
    { min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur' },
  ],
  category_id: [{ required: true, message: '请选择商品分类', trigger: 'change' }],
  pic: [{ required: true, message: '请上传商品主图', trigger: 'change' }],
  albumPics: [
    {
      validator: (rule, value, callback) => {
        if (!galleryList.value || galleryList.value.length !== 8) {
          callback(new Error('必须上传8张商品展示图'))
        } else {
          callback()
        }
      },
      trigger: 'change',
    },
    {
      validator: (rule, value, callback) => {
        const invalidFiles = galleryList.value.filter((file) => file.size > 5 * 1024 * 1024)
        if (invalidFiles.length > 0) {
          callback(new Error(`每张图片不能超过5MB，当前有${invalidFiles.length}张图片过大`))
        } else {
          callback()
        }
      },
      trigger: 'change',
    },
  ],
  inventory: [{ required: true, message: '请输入商品库存', trigger: 'blur' }],
  detail_html: [{ min: 0, max: 1000, message: '最多1000个字符', trigger: 'blur' }],
  brand_name: [{ required: true, message: '请输入商品品牌名称,若无品牌则填写无', trigger: 'blur' }],
}
// 添加尺寸（与添加属性类型逻辑类似）
const addSize = () => {
  if (newSize.value.trim()) {
    if (!availableSizes.value.includes(newSize.value.trim())) {
      // 添加到可用尺寸列表
      availableSizes.value.push(newSize.value.trim())

      // 同步到所有变体的size数组
      form.productVariant.forEach((item) => {
        if (!item.size.includes(newSize.value.trim())) {
          item.size.push(newSize.value.trim())
        }
      })

      newSize.value = ''
      showSizeInput.value = false
    } else {
      ElMessage.warning('该尺寸类型已存在')
    }
  }
}

// 删除尺寸（与删除属性类型逻辑类似）
const removeSize = (index: number) => {
  const removedSize = availableSizes.value[index]
  availableSizes.value.splice(index, 1)

  // 从所有变体中移除该尺寸
  form.productVariant.forEach((item) => {
    item.size = item.size.filter((s) => s !== removedSize)
  })
}

// 处理衣装类开关变化
const handleClothingSwitchChange = (isClothing: boolean) => {
  if (!isClothing) {
    // 关闭时清空所有尺寸
    availableSizes.value = []
    form.productVariant.forEach((item) => {
      item.size = []
    })
  }
}

// 处理变体开关变化
const handleVariantSwitchChange = (value:any) => {
  console.log(form.enableVariant)

  if (!value) {
    // 关闭变体时，只保留一行
    form.productVariant = [
      {
        image: null,
        variantPic: null,
        variantId: 1,
        productUPC: '',
        price: form.productVariant[0]?.price || null,
        inventory: form.productVariant[0]?.inventory || null,
        sellerSKU: form.productVariant[0]?.sellerSKU || '',
        productAttr: '',
        attributes: {} as Record<string, string>,
        size: [],
      },
    ]
  }
}

// 添加变体行
const addVariantRow = () => {
  const newId =
    form.productVariant.length > 0
      ? Math.max(...form.productVariant.map((item) => item.variantId)) + 1
      : 1

  // 初始化attributes对象，确保包含所有已定义的属性类型
  const attributes: Record<string, string> = {}
  form.variantAttributes.forEach((attr) => {
    attributes[attr] = ''
  })

  form.productVariant.push({
    image: null,
    variantPic: null,
    variantId: newId,
    productUPC: '',
    price: null,
    inventory: null,
    sellerSKU: '',
    productAttr: '',
    attributes: {} as Record<string, string>,
    size: [...availableSizes.value],
  })
}
// 监听attributes变化并更新productAttr
const handleAttributesChange = (row: any, index: number) => {
  console.log(row)

  updateProductAttr(row, index)
}
const updateProductAttr = (row: any, index: number) => {
  // 确保 row 和 attributes 存在
  if (!row || !row.attributes) {
    row.attributes = {}
  }

  // 将非空属性值用"+"号拼接
  row.productAttr = Object.entries(row.attributes)
    .map(([key, value]) => `${key}:${value}`) // 格式为"属性名+属性值"
    .join(',') // 多个属性用逗号分隔

  console.log('Updated productAttr:', row.productAttr)
}
// 删除变体行
const removeVariantRow = (index) => {
  if (form.productVariant.length <= 1) {
    ElMessage.warning('至少需要保留一个变体')
    return
  }
  form.productVariant.splice(index, 1)
}
// 添加属性类型
const addAttributeType = () => {
  if (newAttribute.value.trim()) {
    if (!form.variantAttributes.includes(newAttribute.value.trim())) {
      // 添加新属性类型
      form.variantAttributes.push(newAttribute.value.trim())

      // 为所有现有变体行添加该属性字段
      form.productVariant.forEach((row) => {
        if (!row.attributes[newAttribute.value.trim()]) {
          row.attributes[newAttribute.value.trim()] = ''
        }
      })

      newAttribute.value = ''
      showAttributeInput.value = false
    } else {
      ElMessage.warning('该属性类型已存在')
    }
  }
}

// 删除属性类型
const removeAttributeType = (index: number) => {
  const attrToRemove = form.variantAttributes[index]

  // 从属性类型列表中移除
  form.variantAttributes.splice(index, 1)

  // 从所有变体行的attributes对象中移除该属性
  form.productVariant.forEach((row) => {
    if (row.attributes[attrToRemove]) {
      delete row.attributes[attrToRemove]
    }
  })
}
// 处理变体图片上传
const handleVariantImageChange = (file: UploadUserFile, row: any) => {
  // 检查文件类型
  const isImage = file.raw?.type === 'image/jpeg' || file.raw?.type === 'image/png'
  if (!isImage) {
    ElMessage.error('商品图片只能是JPG或PNG格式!')
    return false
  }

  // 检查文件大小
  const isLt5M = file.size! / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('商品图片大小不能超过5MB!')
    return false
  }

  // 检查文件对象
  if (!file.raw) {
    ElMessage.error('无法获取文件对象!')
    return false
  }

  // 保存原始文件对象
  row.variantPic = file.raw

  // 设置预览
  const reader = new FileReader()
  reader.onload = (e: any) => {
    row.image = e.target.result
  }
  reader.readAsDataURL(file.raw)

  console.log('变体图片已保存:', file.raw)
  return false
}

// 获取商品详情并填充表单
const fetchProductDetail = async () => {
  if (!productId.value) {
    ElMessage.error('无效的商品ID')
    router.push('/main/product/list')
    return
  }

  loading.value = true
  try {
    // 获取当前登录的商家ID
    const userStore = useUserStore()
    const sellerId = userStore.userInfo?.id

    if (!sellerId) {
      ElMessage.error('未获取到商家ID，请重新登录')
      router.push('/')
      return
    }

    console.log(`正在获取商家(${sellerId})的商品(${productId.value})详情...`)

    // 调用商家商品详情接口
    const response = await getSellerProductDetail(sellerId, productId.value)

    if (response.code === 1 && response.data) {
      const productData = response.data
      console.log('成功获取到商品详情:', productData)

      // 填充表单数据
      form.id = productData.id
      form.name = productData.name
      form.brand_id = productData.brandId
      form.category_id = productData.categoryId
      form.out_product_id = productData.outProductId || ''
      form.pic = productData.pic || ''
      form.album_pics = productData.albumPics || ''
      form.introduce_pics = productData.introductPics || ''
      form.product_pdf = productData.pdfDocument || ''
      form.publish_status = productData.publishStatus
      form.weight=productData.weight
      form.sort = productData.sort || 0
      form.price = productData.price
      form.unit = productData.unit || '件'
      form.detail_html = productData.detailHtml || ''
      form.brand_name = productData.brandName || ''
      form.product_category_name = productData.productCategoryName || ''
      form.category_id = productData.categoryId // 填充下拉框选择值
      form.package = productData.packageInfo
      form.enableVariant = productData.enableVariant === 1 ? true : false
      // 恢复商品尺寸与属性类型列表信息
      if(productData.productVariant.length>0){
        if(productData.productVariant[0].size.length>0){
          form.isclothing=true
          for (let i = 0; i < productData.productVariant[0].length; i++) {
            form.productVariant[i].size=productData.productVariant[0].size
          }
            availableSizes.value=productData.productVariant[0].size
        }
        form.variantAttributes = productData.productVariant[0].productAttr
          .split(',')
          .map((item: any) => item.split(':')[0])
          .filter(Boolean) || []
          console.log('商品的属性类型列表：',form.variantAttributes);
      }
        
      form.productVariant = productData.productVariant || []
      if(productData.productVariant.length>0){
        form.productVariant.forEach((item) => {
          // 确保 attributes 初始化为空对象
          item.attributes = {}
          if (item.productAttr) {
            item.attributes = item.productAttr.split(',').reduce(
              (acc, pair) => {
                const [key, value] = pair.split(':').map((item) => item.trim())
                if (key && value) {
                  acc[key] = value
                }
                return acc
              },
              {} as Record<string, string>,
            )
          }
          console.log('转换结果:', item.attributes)
        })
      }
      const variantPic = productData.variantPics ? productData.variantPics.split(',') : []
      console.log('当前商品：', form)
      for (let i = 0; i < form.productVariant.length; i++) {
        form.productVariant[i].image = variantPic[i]
      }
      // 处理相册图片
      if (productData.albumPics) {
        try {
          console.log('原始相册图片数据:', productData.albumPics)
          let albumUrls = []

          // 确保albumPics是字符串类型并处理它
          if (typeof productData.albumPics === 'string') {
            // 使用正则表达式查找所有的图片URL，以常见图片扩展名结尾
            const urlRegex =
              /(https?:\/\/[^\s,'"]+\.(jpeg|jpg|png|gif|webp|JPEG|JPG|PNG|GIF|WEBP)(?:\?[^\s,'"]*)?)/g
            const matches = productData.albumPics.match(urlRegex)

            if (matches && matches.length > 0) {
              albumUrls = matches
              console.log('通过正则表达式提取的URL:', albumUrls)
            } else {
              // 回退方法：尝试基于一些启发式规则分割
              const albumPicsStr = productData.albumPics
              let currentUrl = ''
              let result = []

              // 遍历字符串查找合法URL
              for (let i = 0; i < albumPicsStr.length; i++) {
                currentUrl += albumPicsStr[i]

                // 当找到图片扩展名后的逗号或字符串结束时，认为一个URL结束
                if (
                  (currentUrl.match(/\.(jpeg|jpg|png|gif|webp)$/i) &&
                    (i + 1 >= albumPicsStr.length || albumPicsStr[i + 1] === ',')) ||
                  i + 1 >= albumPicsStr.length
                ) {
                  if (currentUrl.trim() && currentUrl.startsWith('http')) {
                    result.push(currentUrl.trim())
                  }
                  // 跳过逗号
                  if (i + 1 < albumPicsStr.length && albumPicsStr[i + 1] === ',') {
                    i++
                  }
                  currentUrl = ''
                }
              }

              if (result.length > 0) {
                albumUrls = result
                console.log('通过扩展名分割提取的URL:', albumUrls)
              }
            }
          } else if (Array.isArray(productData.albumPics)) {
            // 如果是数组，直接使用
            albumUrls = productData.albumPics
          }

          console.log('处理后的相册URL列表:', albumUrls)

          // 设置相册预览列表
          galleryList.value = albumUrls.map((url: string, index: number) => ({
            name: `相册图${index + 1}`,
            url: url,
            uid: -(index + 1),
          }))

          console.log('相册图片处理完成，共 ' + galleryList.value.length + ' 张')
        } catch (error) {
          console.error('处理相册图片出错:', error)
          galleryList.value = []
        }
      } else {
        console.log('商品没有相册图片')
        galleryList.value = []
      }
      // 处理图文介绍图片
      if (productData.introductPics) {
        try {
          let introductUrls = []

          // 确保introductPics是字符串类型并处理它
          if (typeof productData.introductPics === 'string') {
            // 使用正则表达式查找所有的图片URL
            const urlRegex = /(https?:\/\/[^\s,'"]+\.(jpeg|jpg|png|gif|webp)(?:\?[^\s,'"]*)?)/g
            const matches = productData.introductPics.match(urlRegex)

            if (matches && matches.length > 0) {
              introductUrls = matches
            }
          } else if (Array.isArray(productData.introductPics)) {
            // 如果是数组，直接使用
            introductUrls = productData.introductPics
          }

          // 设置图文介绍预览列表
          graphicIntroductList.value = introductUrls.map((url: string, index: number) => ({
            name: `图文介绍图${index + 1}`,
            url: url,
            uid: -(index + 1000), // 使用不同的uid范围避免冲突
          }))
        } catch (error) {
          console.error('处理图文介绍图片出错:', error)
          graphicIntroductList.value = []
          graphicIntroductFiles.value = []
        }
      } else {
        graphicIntroductList.value = []
        graphicIntroductFiles.value = []
      }

      // 处理PDF文档
      if (productData.pdfDocument) {
        form.product_pdf = productData.pdfDocument
        pdfFile.value = {
          name: `${form.name}介绍文档.pdf`,
          url: productData.pdfDocument,
          type: 'application/pdf',
        } as unknown as File
      } else {
        pdfFile.value = null
        form.product_pdf = ''
      }

      console.log('商品详情加载完成')
      ElMessage.success('商品详情加载成功')
    } else {
      ElMessage.error(response.msg || '获取商品详情失败')
      console.error('获取商品详情失败:', response)
      router.push('/main/product/list')
    }
  } catch (error) {
    console.error('获取商品详情出错:', error)
    ElMessage.error('获取商品详情失败，请稍后重试')
    router.push('/main/product/list')
  } finally {
    loading.value = false
  }
}

// 加载分类数据
const fetchCategories = async () => {
  try {
    const res = await getCategoryTree()
    if (res.code === 1 && res.data) {
      categoryOptions.value = res.data
      let flatCategories: { id: number; fullName: string }[] = []

      // 处理分类数据，找出所有三级分类，并构建完整路径名称
      const processCategories = (categories: any[], parentName = '') => {
        for (const category of categories) {
          const currentName = parentName ? `${parentName} > ${category.name}` : category.name

          if (category.level === 2 || !category.children || category.children.length === 0) {
            // 这是一个叶子节点，添加到结果中
            flatCategories.push({
              id: category.id,
              fullName: currentName,
            })
          }

          if (category.children && category.children.length > 0) {
            processCategories(category.children, currentName)
          }
        }
      }

      processCategories(res.data)
      thirdLevelCategories.value = flatCategories
    }
  } catch (error) {
    console.error('获取分类数据失败:', error)
    ElMessage.error('获取分类数据失败，请稍后重试')
  }
}

// 处理主图上传
const handleMainImageChange = (file: UploadUserFile) => {
  // 检查文件类型
  const isImage = file.raw?.type === 'image/jpeg' || file.raw?.type === 'image/png'
  if (!isImage) {
    ElMessage.error('商品主图只能是JPG或PNG格式!')
    return false
  }

  // 检查文件对象
  if (!file.raw) {
    ElMessage.error('无法获取文件对象!')
    return false
  }

  // 检查文件大小
  const isLt5M = file.size! / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('商品主图大小不能超过5MB!')
    return false
  }

  // 如果已经有主图，先计算减去当前主图后的总大小
  let additionalSize = file.size!
  if (mainPicFile.value) {
    additionalSize = file.size! - mainPicFile.value.size
  }

  // 保存原始文件对象
  mainPicFile.value = file.raw

  // 设置预览
  const reader = new FileReader()
  reader.onload = (e: any) => {
    form.pic = e.target.result
  }
  reader.readAsDataURL(file.raw)

  console.log('主图文件已保存:', file.raw.name, file.raw.size, file.raw.type)
  return false // 阻止自动上传
}

// 处理相册上传
const handleGalleryChange = (file: UploadUserFile, fileList: UploadUserFile[]) => {
  // 检查文件类型和大小
  const isImage = file.raw?.type === 'image/jpeg' || file.raw?.type === 'image/png'
  if (!isImage) {
    ElMessage.error('商品图片只能是JPG或PNG格式!')
    return false
  }

  // 检查单个文件大小
  const isLt5M = file.size! / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('商品图片大小不能超过5MB!')
    return false
  }

  // 限制图片数量
  if (fileList.length > 8) {
    ElMessage.warning('最多只能上传5张商品展示图')
    fileList.pop() // 移除最后一个
    return false
  }

  // 检查文件对象
  if (!file.raw) {
    ElMessage.error('无法获取文件对象!')
    return false
  }

  // 更新相册文件列表
  albumPicFiles.value = []
  fileList.forEach((item) => {
    if (item.raw) {
      albumPicFiles.value.push(item.raw)
    }
  })

  // 更新预览图
  const reader = new FileReader()
  reader.onload = () => {
    // 更新相册预览
    galleryList.value = fileList

    // 更新album_pics字段
    const galleries = fileList
      .map((item) => {
        if (item.url) return item.url
        if (item.raw) {
          const rawFile = item.raw as File
          return URL.createObjectURL(rawFile)
        }
        return ''
      })
      .filter((url) => url !== '')

    form.album_pics = galleries.join(',')
  }
  reader.readAsDataURL(file.raw)

  console.log('相册文件已更新, 当前数量:', albumPicFiles.value.length)
  return false // 阻止自动上传
}
const updateIntroductionPics = () => {
  // 从graphicIntroductList中提取所有URL
  const urls = graphicIntroductList.value.map((item) => item.url).filter(Boolean)

  // 转换为逗号分隔的字符串，与后端格式匹配
  form.introduce_pics = urls.join(',')
  console.log('已更新图文介绍图片列表:', form.introduce_pics)
}
// 处理图文介绍图片上传
const handleIntroductionChange = (file: UploadUserFile, fileList: UploadUserFile[]) => {
  console.log('当前文件列表：', fileList)

  // 检查文件类型和大小
  const isImage = file.raw?.type === 'image/jpeg' || file.raw?.type === 'image/png'
  if (!isImage) {
    ElMessage.error('商品图片只能是JPG或PNG格式!')
    return false
  }

  // 检查单个文件大小
  const isLt5M = file.size! / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('商品图片大小不能超过5MB!')
    return false
  }

  // 限制图片数量
  if (fileList.length > 5) {
    ElMessage.warning('最多只能上传5张商品展示图')
    fileList.pop() // 移除最后一个
    return false
  }

  // 检查文件对象
  if (!file.raw) {
    ElMessage.error('无法获取文件对象!')
    return false
  }

  // 更新文件列表
  graphicIntroductList.value = fileList
  // 更新原始文件列表
  graphicIntroductFiles.value.push(file.raw)

  // 如果文件没有URL，创建预览URL
  if (!file.url && file.raw) {
    const reader = new FileReader()
    reader.onload = () => {
      file.url = reader.result as string
      graphicIntroductList.value = [...fileList]
      updateIntroductionPics() // 更新图文介绍字段
    }
    reader.readAsDataURL(file.raw)
  } else {
    updateIntroductionPics() // 更新图文介绍字段
  }
  console.log('当前图文介绍文件列表:', graphicIntroductFiles.value)
  return false
}
const beforePdfUpload = (file: File) => {
  const isPdf = file.type === 'application/pdf'
  const isLt10M = file.size / 1024 / 1024 < 8

  if (!isPdf) {
    ElMessage.error('只能上传PDF格式的文件!')
    return false
  }

  if (!isLt10M) {
    ElMessage.error('PDF文件大小不能超过8MB!')
    return false
  }
  return true
}

// 处理PDF文件变化
const handlePdfChange = (file: UploadUserFile, fileList: UploadUserFile[]) => {
  if (!file.raw) {
    ElMessage.error('无法获取文件对象!');
    return false;
  }

  // 验证文件
  if (!beforePdfUpload(file.raw)) {
    return false;
  }

  // 添加到文件列表
  pdfFiles.value.push(file.raw);
  pdfFileList.value = fileList;

  // 更新表单数据（假设 form.product_pdf 存储所有PDF的URL）
  form.product_pdf = pdfFileList.value.map(f => f.url || '').join(',');

  console.log('当前PDF文件列表:', pdfFiles.value);
  return false;
}

// 处理PDF文件移除
const handlePdfRemove = () => {
  if (pdfFile.value) {
    console.log('移除PDF文件:', pdfFile.value.name)
  }
  pdfFile.value = null
  form.product_pdf = ''
}
// 处理图文介绍图片移除
const handleIntroductionRemove = (file: UploadUserFile) => {
  // 1. 从预览列表中移除图片
  const previewIndex = graphicIntroductList.value.findIndex((item) => item.uid === file.uid)
  if (previewIndex !== -1) {
    graphicIntroductList.value.splice(previewIndex, 1)
  }

  // 2. 从原始文件列表中移除对应的文件
  if (file.raw) {
    const fileIndex = graphicIntroductFiles.value.findIndex(
      (f) => f.name === file.name && f.size === file.size,
    )
    if (fileIndex !== -1) {
      graphicIntroductFiles.value.splice(fileIndex, 1)
    }
  }

  // 3. 更新表单中的图文介绍图片字段
  updateIntroductionPics()

  console.log('当前图文介绍文件列表:', graphicIntroductFiles.value)
}
// 更新album_pics字段
const updateAlbumPics = () => {
  // 从galleryList中提取所有URL，包括已有的和新上传的
  const urls = galleryList.value.map((item) => item.url).filter(Boolean) // 过滤掉undefined和null

  // 转换为逗号分隔的字符串，与后端格式匹配
  form.album_pics = urls.join(',')
  console.log('已更新相册图片列表:', form.album_pics)
}
// 处理相册图片删除
const handleGalleryRemove = (file: UploadUserFile) => {
  const index = galleryList.value.indexOf(file)
  if (index !== -1) {
    // 更新预览图片
    galleryList.value.splice(index, 1)
    // 如果是新上传的文件，从原始文件列表中移除
    if (file.raw) {
      const rawIndex = albumPicFiles.value.findIndex((f) => f === file.raw)
      if (rawIndex !== -1) {
        albumPicFiles.value.splice(rawIndex, 1)
      }
    }

    // 更新album_pics字段
    updateAlbumPics()
  }
  console.log('当前相册文件列表:', albumPicFiles.value)
}

// 更新商品接口
const updateProduct = async (productData: any): Promise<any> => {
  try {
    // 获取当前登录商家ID
    const userStore = useUserStore()
    const sellerId = userStore.userInfo?.id
    console.log('获取到的商家ID:', sellerId)

    if (!sellerId) {
      ElMessage.error('未获取到商家ID，请重新登录')
      throw new Error('未获取到商家ID，请重新登录')
    }
    // 准备变体图片数据
    const variantPics = form.productVariant.map((variant) => variant.variantPic)
    
    // 转换字段名称以匹配后端PmsProduct实体类
    // 注意：确保不包含任何图片相关字段
    const product = {
      id: productData.id,
      productSnapshotId: productData.product_snapshot_id || null,
      brandId: productData.brand_id,
      categoryId: productData.category_id,
      outProductId: productData.out_product_id || '',
      name: productData.name,
      publishStatus: productData.publish_status,
      sort: productData.sort,
      inventory: productData.inventory,
      price: productData.price,
      unit: productData.unit,
      weight: productData.weight,
      detailHtml: productData.detail_html,
      brandName: productData.brand_name,
      productCategoryName: productData.product_category_name,
      productAttr: productData.product_attr,
      enableVariant:productData.enableVariant===true?1:0,
      productVariant: productData.productVariant.map(
        ({ variantPic, attributes, image, ...rest }) => rest,
      ),
      // 严格不包含pic和albumPics字段
    }

    console.log('准备调用updateSellerProduct API...')
    console.log('商品基本数据:', {
      id: product.id,
      name: product.name,
      brandId: product.brandId,
      categoryId: product.categoryId,
      price: product.price,
    })
    console.log(
      '图片信息 (单独传递) - 主图:',
      mainPicFile.value ? '已更新' : '无更新',
      '相册图片:',
      albumPicFiles.value.length,
      '张',
    )
    console.log('上传的图片',mainPicFile.value,albumPicFiles.value, graphicIntroductFiles.value, pdfFiles.value)

    // 调用API模块中的方法更新商品，文件单独传递
    const result = await updateSellerProduct(
      sellerId,
      productId.value,
      product,
      mainPicFile.value,
      albumPicFiles.value,
      graphicIntroductFiles.value,
      pdfFiles.value,
      variantPics,
    )

    console.log('updateSellerProduct API返回结果:', result)
    return result
  } catch (error) {
    console.error('更新商品失败:', error)
    throw error
  }
}

// 取消
const handleCancel = () => {
  ElMessage.warning('正在返回商品列表')
  router.push('/main/product/list')
}

// 处理表单提交
const handleSubmit = async () => {
  // 验证表单
  if (!form.name) {
    ElMessage.error('请输入商品名称')
    return
  }

  if (!form.category_id) {
    ElMessage.error('请选择商品分类')
    return
  }

  try {
    submitLoading.value = true

    // 准备分类名称和品牌名称
    if (form.category_id) {
      const category = thirdLevelCategories.value.find((item) => item.id === form.category_id)
      if (category) {
        form.product_category_name = category.fullName
      }
    }

    if (form.brand_id) {
      const brand = brandOptions.find((item) => item.id === form.brand_id)
      if (brand) {
        form.brand_name = brand.name
      }
    }

    console.log('更新的数据：', form)

    const result = await updateProduct(form)

    if (result.code === 1) {
      ElMessage.success('商品更新成功')
      // 延迟跳转，确保用户看到成功消息
      setTimeout(() => {
        router.push('/main/product/list')
      }, 1500)
    } else {
      ElMessage.error(result.msg || '商品更新失败')
    }
  } catch (error: any) {
    console.error('更新商品出错:', error)

    let errorMessage = '商品更新失败，请稍后重试'

    // 处理常见HTTP错误
    if (error.response) {
      const status = error.response.status
      switch (status) {
        case 400:
          errorMessage = '请求参数错误，请检查输入'
          break
        case 401:
          errorMessage = '登录已过期，请重新登录'
          break
        case 403:
          errorMessage = '您没有权限执行此操作'
          break
        case 404:
          errorMessage = '商品不存在或已被删除'
          break
        case 500:
          errorMessage = '服务器错误，请联系管理员'
          break
      }

      // 如果有详细错误响应，显示它
      if (error.response.data && error.response.data.message) {
        errorMessage += `\n详情: ${error.response.data.message}`
      }
    }

    // 对网络错误的处理
    if (error.name === 'NetworkError' || error.message.includes('network')) {
      errorMessage = '网络连接错误，请检查您的网络连接'
    }

    // 对超时错误的处理
    if (error.message.includes('timeout')) {
      errorMessage = '请求超时，请稍后重试'
    }

    ElMessage.error(errorMessage)

    // 针对401错误，自动跳转到登录页
    if (error.response && error.response.status === 401) {
      setTimeout(() => {
        router.push('/')
      }, 1500)
    }
  } finally {
    submitLoading.value = false
  }
}

// 初始化加载分类数据和商品详情
onMounted(async () => {
  await fetchCategories()
  await fetchProductDetail()
})
</script>

<style scoped lang="scss">
.page-container {
  padding: 16px;

  .page-card {
    :deep(.el-card__header) {
      padding: 16px 20px;
    }

    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .page-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }
  }

  .form-section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;

    .section-title {
      margin: 0 0 20px;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      border-left: 4px solid var(--el-color-primary);
      padding-left: 10px;
    }

    &:last-child {
      margin-bottom: 0;
    }
    .weight-input-group {
      display: flex;
      width: 100%;
      :deep(.el-input__wrapper) {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }

      :deep(.el-select .el-input__wrapper) {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-right: 0;
      }
    }
    .auto-generated-id {
      color: #888;
      font-style: italic;
    }

    .variant-actions {
      margin-top: 10px;
      text-align: right;
    }

    /* 调整表格内输入框的样式 */
    .el-table :deep(.el-input-number) {
      width: 100%;
    }

    .el-table :deep(.el-input-number__decrease),
    .el-table :deep(.el-input-number__increase) {
      display: none;
    }
    .variant-attributes {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 4px;

      .attributes-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        h4 {
          margin: 0;
          font-size: 14px;
          color: #606266;
        }
      }

      .attribute-input {
        margin-bottom: 15px;
        display: flex;
        align-items: center;
      }

      .attribute-types {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .el-tag {
          margin-bottom: 5px;
        }
      }

      .no-attributes-tip {
        margin-top: 8px;
        padding: 8px 12px;
        background-color: #f5f7fa;
        border-radius: 4px;
        color: #606266;
        font-size: 13px;
        display: flex;
        align-items: center;

        .el-icon {
          margin-right: 6px;
          color: #909399;
        }
      }

      .attribute-types {
        margin-top: 12px;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }
    }
    .size-management {
      margin: 5px 0;
      padding: 5px;
      background-color: #f8f9fa;
      border-radius: 4px;

      .size-input-group {
        display: flex;
        margin-bottom: 15px;
      }

      .size-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .el-tag {
          margin-bottom: 5px;
        }
      }
    }
    /* 变体图片上传样式 */
    .variant-image-uploader {
      width: 50px;
      height: 50px;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
      display: flex; /* 添加flex布局 */
      align-items: center; /* 垂直居中 */
      justify-content: center; /* 水平居中 */

      &:hover {
        border-color: var(--el-color-primary);
      }

      .variant-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .upload-placeholder {
        display: flex;
        align-items: center; /* 垂直居中 */
        justify-content: center; /* 水平居中 */
        height: 100%;
        width: 100%; /* 确保占满整个区域 */
        color: #8c939d;

        .el-icon {
          font-size: 20px; /* 调整为更合适的尺寸 */
          margin: 0; /* 移除不必要的margin */
        }
      }
    }

    .upload-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 8px;
    }
  }

  .field-tip {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
    display: flex;
    align-items: center;

    .el-icon {
      margin-right: 5px;
      color: #909399;
    }
  }

  .main-image-uploader {
    :deep(.el-upload) {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      width: 200px;
      height: 200px;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
      }
    }

    .main-image {
      width: 200px;
      height: 200px;
      display: block;
      object-fit: contain;
    }

    .upload-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      color: #909399;

      .el-icon {
        font-size: 28px;
        margin-bottom: 8px;
      }

      .upload-text {
        font-size: 14px;
      }
    }
  }

  /* 商品属性部分样式 */
  .attr-container {
    display: flex;
    width: 100%;
    gap: 20px;
    height: 400px;

    .attr-child {
      flex: 1;
      box-sizing: border-box;
    }
    .attr-selection-panel {
      flex: 0 0 40%;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      overflow: hidden;
      height: 100%;

      :deep(.el-tabs) {
        height: 100%;

        .el-tabs__content {
          padding: 15px;
          height: calc(100% - 40px);
          overflow-y: auto;
        }
      }

      .attr-group {
        margin-bottom: 10px;

        .attr-checkbox {
          margin-right: 10px;
          margin-bottom: 10px;
        }
      }
    }

    .attr-preview-panel {
      flex: 0 0 56%;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      height: 100%;

      .panel-header {
        padding: 10px 15px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #ebeef5;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .panel-title {
          font-weight: 600;
          font-size: 14px;
        }

        .attr-count {
          font-size: 12px;
          color: #909399;

          b {
            color: #409eff;
          }
        }
      }

      .attr-tags-container {
        padding: 15px;
        flex: 1;
        overflow-y: auto;

        .no-attrs {
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;

          .empty-tip {
            margin: 0;
            margin-top: 10px;
            font-size: 14px;
            color: #909399;
          }

          .empty-tip-sub {
            margin: 5px 0 0 0;
            font-size: 12px;
            color: #c0c4cc;
          }
        }

        .attr-list {
          display: flex;
          // flex-direction: column;
          flex-wrap: wrap;
          gap: 5%;
          width: 100%;
          .attr-item {
            width: 30%;
            box-sizing: border-box;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #409eff;

            .attr-button {
              width: 100% !important;
              flex-grow: 1;
            }
            .attr-item-content {
              display: flex;
              flex-direction: column;
              gap: 5px;

              .attr-item-key {
                font-weight: 600;
                font-size: 14px;
                color: #303133;
              }

              .attr-item-value {
                font-size: 13px;
                color: #606266;
              }
            }

            .attr-item-actions {
              display: flex;
              gap: 5px;
            }
          }
        }
      }
    }
  }

  .attr-tips {
    margin-top: 10px;
  }

  .upload-tip {
    color: #999;
    font-size: 12px;
    margin-top: 5px;
  }

  .gallery-uploader {
    :deep(.el-upload--picture-card) {
      width: 148px;
      height: 148px;
      line-height: 148px;
    }
  }
}
</style>
