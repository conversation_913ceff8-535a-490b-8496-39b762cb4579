<template>
  <div class="page-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="page-header">
          <h2 class="page-title">添加商品</h2>
          <div class="page-actions">
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
              添加商品
            </el-button>
          </div>
        </div>
      </template>

      <div class="page-content">
        <el-form
          label-position="top"
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          class="product-form"
          @submit.prevent
        >
          <!-- 基本信息 -->
          <div class="form-section">
            <h3 class="section-title">基本信息</h3>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="商品名称" prop="name">
                  <el-input v-model="form.name" placeholder="请输入商品名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="商品分类" prop="category_id">
                  <el-select
                    v-model="form.category_id"
                    placeholder="请选择商品分类"
                    clearable
                    filterable
                  >
                    <el-option
                      v-for="category in thirdLevelCategories"
                      :key="category.id"
                      :label="category.fullName"
                      :value="category.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="品牌" prop="brand_name">
                  <el-input v-model="form.brand_name" placeholder="请输入品牌名称" clearable />
                </el-form-item>
              </el-col>
              <el-col
                :span="12"
                v-if="
                  userStore.userInfo.role === '超级管理员' || userStore.userInfo.role === '管理员'
                "
              >
                <el-form-item
                  v-if="
                    userStore.userInfo.role === '超级管理员' || userStore.userInfo.role === '管理员'
                  "
                  label="商品排序"
                  prop="sort"
                >
                  <el-input-number v-model="form.sort" :min="0" style="width: 100%" />
                  <div class="field-tip">
                    <el-icon><InfoFilled /></el-icon>
                    <span>排序值越小越靠前，用于控制商品在商城中的展示顺序</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col>
                <el-form-item label="上架状态" prop="publish_status">
                  <el-radio-group v-model="form.publish_status">
                    <el-radio :label="1">上架</el-radio>
                    <el-radio :label="0">下架</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 商品图片 -->
          <div class="form-section">
            <h3 class="section-title">商品图片</h3>
            <el-form-item label="商品主图" prop="pic">
              <el-upload
                class="main-image-uploader"
                action="#"
                :auto-upload="false"
                :show-file-list="false"
                :on-change="handleMainImageChange"
              >
                <img v-if="form.pic" :src="form.pic" class="main-image" />
                <div v-else class="upload-placeholder">
                  <el-icon><Plus /></el-icon>
                  <div class="upload-text">上传主图</div>
                </div>
              </el-upload>
              <div class="upload-tip">建议尺寸：800x800px，支持jpg、png格式</div>
            </el-form-item>

            <el-form-item label="商品相册" prop="albumPics">
              <el-upload
                class="gallery-uploader"
                action="#"
                :auto-upload="false"
                list-type="picture-card"
                :on-change="handleGalleryChange"
                :on-remove="handleGalleryRemove"
                :file-list="galleryList"
                multiple
                :limit="8"
              >
                <el-icon><Plus /></el-icon>
              </el-upload>
              <div class="upload-tip">请上传8张商品展示图，建议尺寸：800x800px</div>
            </el-form-item>
          </div>
          <div class="form-section">
            <h3 class="section-title">商品包裹信息</h3>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="商品包裹重量">
                  <div class="weight-input-group">
                    <el-select v-model="weightUnit" style="width: 110px" placeholder="单位">
                      <el-option label="g" value="g" />
                      <el-option label="kg" value="kg" />
                      <el-option label="lb" value="lb" />
                      <el-option label="oz" value="oz" />
                    </el-select>
                    <el-input
                      style="width: 100%"
                      v-model="form.package.weight"
                      placeholder="请输入重量"
                    >
                    </el-input>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="商品包裹长度">
                  <el-input v-model="form.package.length" placeholder="请输入商品包裹长度">
                    <template #append>cm</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="商品包裹宽度">
                  <el-input v-model="form.package.width" placeholder="请输入商品包裹宽度">
                    <template #append>cm</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="商品包裹高度">
                  <el-input v-model="form.package.height" placeholder="请输入商品包裹高度">
                    <template #append>cm</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 商品属性 -->
          <!-- 商品销售信息部分 -->
          <div class="form-section">
            <h3 class="section-title">商品销售信息</h3>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="是否开启商品变体">
                  <el-switch v-model="form.enableVariant" @change="handleVariantSwitchChange" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="是否是衣装类商品">
                  <el-switch v-model="form.isclothing" @change="handleClothingSwitchChange" />
                </el-form-item>
              </el-col>
            </el-row>
            <!-- 变体属性类型管理（仅在开启变体时显示） -->
            <div v-if="form.enableVariant" class="variant-attributes">
              <div class="attributes-header">
                <h4>变体属性类型</h4>
                <el-button
                  type="primary"
                  size="small"
                  @click="showAttributeInput = true"
                  v-if="!showAttributeInput"
                >
                  <el-icon><Plus /></el-icon>
                  添加属性类型
                </el-button>
              </div>

              <!-- 添加属性类型输入框 -->
              <div v-if="showAttributeInput" class="attribute-input">
                <el-input
                  v-model="newAttribute"
                  placeholder="输入属性类型名称（如：颜色、材质等）"
                  style="width: 300px; margin-right: 10px"
                />
                <el-button type="primary" size="small" @click="addAttributeType"> 确认 </el-button>
                <el-button size="small" @click="showAttributeInput = false"> 取消 </el-button>
              </div>

              <!-- 已添加的属性类型列表 -->
              <div class="attribute-types">
                <el-tag
                  v-for="(attr, index) in form.variantAttributes"
                  :key="index"
                  closable
                  @close="removeAttributeType(index)"
                >
                  {{ attr }}
                </el-tag>
              </div>

              <!-- 属性为空提示 -->
              <div
                v-if="form.variantAttributes.length === 0 && !showAttributeInput"
                class="no-attributes-tip"
              >
                <el-icon><InfoFilled /></el-icon>
                <span>暂无属性类型，请点击上方按钮添加</span>
              </div>
            </div>
            <div v-if="form.isclothing" class="variant-attributes">
              <div class="attributes-header">
                <h4>商品尺寸管理</h4>
                <el-button
                  type="primary"
                  size="small"
                  @click="showSizeInput = true"
                  v-if="!showSizeInput"
                >
                  <el-icon><Plus /></el-icon>
                  添加尺寸类型
                </el-button>
              </div>

              <!-- 添加尺寸输入框（与属性类型输入框结构一致） -->
              <div v-if="showSizeInput" class="attribute-input">
                <el-input
                  v-model="newSize"
                  placeholder="输入尺寸类型（如：S/M/L/XL等）"
                  style="width: 300px; margin-right: 10px"
                />
                <el-button type="primary" size="small" @click="addSize">确认</el-button>
                <el-button size="small" @click="showSizeInput = false">取消</el-button>
              </div>

              <!-- 已添加的尺寸类型列表（与属性类型标签区一致） -->
              <div class="attribute-types">
                <el-tag
                  v-for="(size, index) in availableSizes"
                  :key="index"
                  closable
                  @close="removeSize(index)"
                >
                  {{ size }}
                </el-tag>
              </div>

              <!-- 尺寸为空提示（与属性类型提示一致） -->
              <div v-if="availableSizes.length === 0 && !showSizeInput" class="no-attributes-tip">
                <el-icon><InfoFilled /></el-icon>
                <span>暂无尺寸类型，请点击上方按钮添加</span>
              </div>
            </div>
            <!-- <el-button @click="console.log(form)">查看</el-button> -->
            <el-table :data="form.productVariant" border style="width: 100%">
              <!-- 序号列 -->
              <el-table-column label="序号" width="60">
                <template #default="{ $index }">
                  {{ $index + 1 }}
                </template>
              </el-table-column>

              <!-- 商品ID列 -->
              <el-table-column label="商品ID" width="100">
                <template #default>
                  <span class="auto-generated-id">自动生成</span>
                </template>
              </el-table-column>
              <!-- 动态属性列 -->
              <el-table-column
                v-for="(attr, index) in form.variantAttributes"
                v-if="form.enableVariant"
                :key="index"
                :label="attr"
                width="130"
              >
                <template #default="{ row }">
                  <el-input
                    v-model="row.attributes[attr]"
                    :placeholder="`请输入${attr}`"
                    clearable
                    @change="handleAttributesChange(row, index)"
                  />
                </template>
              </el-table-column>

              <!-- 价格列（可编辑） -->
              <el-table-column label="价格($)" width="150">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.price"
                    :min="0"
                    :precision="2"
                    controls-position="right"
                    placeholder="请输入价格"
                  />
                </template>
              </el-table-column>

              <!-- 库存列（可编辑） -->
              <el-table-column label="当前库存" width="150">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.inventory"
                    :min="0"
                    :step="1"
                    controls-position="right"
                    style="width: 100%"
                    placeholder="请输入库存"
                  />
                </template>
              </el-table-column>

              <el-table-column label="商品图片" width="180">
                <template #default="{ row }">
                  <el-upload
                    class="variant-image-uploader"
                    action="#"
                    :auto-upload="false"
                    :show-file-list="false"
                    :on-change="(file) => handleVariantImageChange(file, row)"
                  >
                    <img v-if="row.image" :src="row.image" class="variant-image" />
                    <div v-else class="upload-placeholder">
                      <el-icon><Plus /></el-icon>
                    </div>
                  </el-upload>
                </template>
              </el-table-column>

              <!-- Seller SKU列（可编辑） -->
              <el-table-column label="Seller SKU(可选)">
                <template #default="{ row }">
                  <el-input v-model="row.sellerSKU" placeholder="请输入SKU" clearable />
                </template>
              </el-table-column>

              <!-- 操作列（仅在开启变体时显示） -->
              <el-table-column v-if="form.enableVariant" label="操作" width="100">
                <template #default="{ $index }">
                  <el-button type="danger" size="small" circle @click="removeVariantRow($index)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 添加行按钮（仅在开启变体时显示） -->
            <div v-if="form.enableVariant" class="variant-actions">
              <el-button type="primary" size="small" @click="addVariantRow">
                <el-icon><Plus /></el-icon>
                添加变体
              </el-button>
            </div>
          </div>

          <div class="form-section">
            <h3 class="section-title">商品详情</h3>
            <el-form-item label="商品详情" prop="detail_html">
              <div class="editor">
                <QuillEditor
                  v-model:content="form.detail_html"
                  :options="editorOptions"
                  contentType="html"
                  style="height: 300px; width: auto"
                />
              </div>
            </el-form-item>
            <el-form-item label="图文介绍" prop="graphic_introduction">
              <el-upload
                list-type="picture-card"
                action="#"
                :auto-upload="false"
                :on-change="handleIntroductionChange"
                :on-remove="handleIntroductionRemove"
                multiple
                :file-list="graphicIntroductList"
              >
                <el-icon><Plus /></el-icon>
              </el-upload>
              <div class="upload-tip">建议尺寸：800x800px，支持jpg、png格式</div>
            </el-form-item>
            <el-form-item label="商品文档(PDF)" prop="pdf_document">
              <el-upload
                class="pdf-uploader"
                action="#"
                :auto-upload="false"
                :show-file-list="true"
                :on-change="handlePdfChange"
                :on-remove="handlePdfRemove"
                :limit="3"
                :file-list="pdfFileList"
                :before-upload="beforePdfUpload"
                accept=".pdf,application/pdf"
              >
                <el-button type="primary" plain>
                  <el-icon><Document /></el-icon>
                  <span>上传PDF文档</span>
                </el-button>
                <template #tip>
                  <div class="upload-tip">仅支持PDF格式，最大10MB</div>
                  <!-- 添加总大小提示 -->
                  <div class="upload-size-info" v-if="pdfFile">
                    <span
                      class="size-text"
                      :class="{
                        warning: pdfFile.size / 1024 / 1024 > 7 && pdfFile.size / 1024 / 1024 <= 10,
                        danger: pdfFile.size / 1024 / 1024 > 10,
                        normal: pdfFile.size / 1024 / 1024 <= 7,
                      }"
                    >
                      PDF大小：{{ (pdfFile.size / 1024 / 1024).toFixed(2) }}MB / 10MB
                    </span>
                    <el-progress
                      class="size-progress"
                      :percentage="(pdfFile.size / 1024 / 1024) * 10"
                      :status="
                        pdfFile.size / 1024 / 1024 > 10
                          ? 'exception'
                          : pdfFile.size / 1024 / 1024 > 7
                            ? 'warning'
                            : ''
                      "
                      :stroke-width="8"
                    ></el-progress>
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </el-card>

    <!-- 添加商品审核弹窗 -->
    <ProductAuditDialog
      v-model:visible="auditDialogVisible"
      :product-name="submittedProductName"
      @view-products="goToProductList"
      @add-another="closeAuditDialog"
    />
  </div>
</template>

<script setup lang="ts">
import { getCategoryTree } from '@/api/category'
import { addProduct } from '@/api/product'
import ProductAuditDialog from '@/components/ProductAuditDialog.vue'
import { useUserStore } from '@/stores/user'
import { Delete, Edit, InfoFilled, Plus, QuestionFilled } from '@element-plus/icons-vue'
import type { UploadUserFile } from 'element-plus'
import { ElMessage } from 'element-plus'
import { computed, nextTick, onMounted, reactive, ref, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { Quill, QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import { login } from '@/api/seller'
const router = useRouter()
const formRef = ref()
const submitLoading = ref(false)
const galleryList = ref<UploadUserFile[]>([])
const graphicIntroductList = ref<UploadUserFile[]>([])
const userStore = useUserStore()

// 分类数据
const categoryOptions = ref<any[]>([])
const thirdLevelCategories = ref<{ id: number; fullName: string }[]>([])

// 品牌选项
const brandOptions = [
  { id: 1, name: '华为' },
  { id: 2, name: '小米' },
  { id: 3, name: '苹果' },
  { id: 4, name: '三星' },
  { id: 5, name: 'OPPO' },
  { id: 6, name: 'vivo' },
]

const editorOptions = reactive({
  modules: {
    toolbar: [
      // 第一行：文本样式
      [{ header: [1, 2, 3, false] }], // 标题
      ['bold', 'italic', 'underline', 'strike'], // 加粗/斜体/下划线/删除线
      [{ color: [] }, { background: [] }], // 文字颜色/背景色

      // 第二行：段落格式
      [{ list: 'ordered' }, { list: 'bullet' }], // 列表
      [{ indent: '-1' }, { indent: '+1' }], // 缩进
      [{ align: [] }], // 对齐方式

      // 第三行：其他功能
      ['blockquote', 'code-block'], // 引用/代码块
      ['link'], // 链接（保留基础链接功能）
      ['clean'], // 清除格式
    ],
  },
  theme: 'snow',
})
// 单位选项
const unitOptions = ['件', '个', '套', '盒', '袋', '箱', '瓶', '千克', '克', '米', '厘米']
// 包裹重量单位
const weightUnit = ref('g')

const showAttributeInput = ref(false)
const newAttribute = ref('')
// 预定义的销售属性
const predefinedAttrs = [
  {
    groupName: '基本属性',
    attrs: ['颜色', '尺寸', '材质', '款式', '重量'],
  },
  {
    groupName: '电子产品',
    attrs: ['内存', '存储容量', '处理器', '屏幕尺寸', '电池容量', '操作系统'],
  },
  {
    groupName: '服装',
    attrs: ['季节', '适用人群', '领型', '袖长', '风格', '版型'],
  },
  {
    groupName: '食品',
    attrs: ['口味', '产地', '保质期', '包装', '净含量'],
  },
]

// 已选择的属性（用于复选框组）
const selectedAttrs = ref(Array(predefinedAttrs.length).fill([]))

// 表单数据
const form = reactive({
  startProductVariant: false,
  product_snapshot_id: null,
  brand_id: null,
  category_id: null,
  out_product_id: '',
  name: '',
  pic: '',
  album_pics: '',
  publish_status: 1,
  package: {
    weight: null,
    height: null,
    width: null,
    length: null,
  },
  variantAttributes: [] as string[],
  enableVariant: false,
  isclothing: false,
  productVariant: [
    {
      image: null,
      variantPic: null,
      variantId: 1, // 变体ID，前端自增
      productUPC: '', // 商品UPC码（可选）
      price: null, // 价格
      inventory: null, // 库存
      sellerSKU: '', // 卖家SKU
      productAttr: '',
      attributes: {} as Record<string, string>, // 动态存储属性值
      size: [] as string[], // 尺寸（可选）
    },
  ],
  sort: 0,
  price: 0,
  unit: '件',
  detail_html: '',
  brand_name: '',
  product_category_name: '',
})

// 在表单数据中添加存储原始文件的字段
const mainPicFile = ref<File | null>(null)
const albumPicFiles = ref<File[]>([])
const graphicIntroductFiles = ref<File[]>([])
const pdfFiles = ref<File[]>([]) // 存储原始文件对象
const pdfFileList = ref<UploadUserFile[]>([])
// 尺寸管理相关状态
const newSize = ref('')
const showSizeInput = ref(false)
const availableSizes = ref<string[]>([])

// 添加尺寸（与添加属性类型逻辑类似）
const addSize = () => {
  if (newSize.value.trim()) {
    if (!availableSizes.value.includes(newSize.value.trim())) {
      // 添加到可用尺寸列表
      availableSizes.value.push(newSize.value.trim())

      // 同步到所有变体的size数组
      form.productVariant.forEach((item) => {
        if (!item.size.includes(newSize.value.trim())) {
          item.size.push(newSize.value.trim())
        }
      })

      newSize.value = ''
      showSizeInput.value = false
    } else {
      ElMessage.warning('该尺寸类型已存在')
    }
  }
}

// 删除尺寸（与删除属性类型逻辑类似）
const removeSize = (index: number) => {
  const removedSize = availableSizes.value[index]
  availableSizes.value.splice(index, 1)

  // 从所有变体中移除该尺寸
  form.productVariant.forEach((item) => {
    item.size = item.size.filter((s) => s !== removedSize)
  })
}

// 处理衣装类开关变化
const handleClothingSwitchChange = (isClothing: boolean) => {
  if (!isClothing) {
    // 关闭时清空所有尺寸
    availableSizes.value = []
    form.productVariant.forEach((item) => {
      item.size = []
    })
  }
}

// 处理变体开关变化
const handleVariantSwitchChange = (value) => {
  console.log(form.enableVariant)

  if (!value) {
    // 关闭变体时，只保留一行
    form.productVariant = [
      {
        image: null,
        variantPic: null,
        variantId: 1,
        productUPC: '',
        price: form.productVariant[0]?.price || null,
        inventory: form.productVariant[0]?.inventory || null,
        sellerSKU: form.productVariant[0]?.sellerSKU || '',
        productAttr: '',
        attributes: {} as Record<string, string>,
        size: [],
      },
    ]
  }
}

// 添加变体行
const addVariantRow = () => {
  const newId =
    form.productVariant.length > 0
      ? Math.max(...form.productVariant.map((item) => item.variantId)) + 1
      : 1

  // 初始化attributes对象，确保包含所有已定义的属性类型
  const attributes: Record<string, string> = {}
  form.variantAttributes.forEach((attr) => {
    attributes[attr] = ''
  })

  form.productVariant.push({
    image: null,
    variantPic: null,
    variantId: newId,
    productUPC: '',
    price: null,
    inventory: null,
    sellerSKU: '',
    productAttr: '',
    attributes: {} as Record<string, string>,
    size: [...availableSizes.value],
  })
}
// 监听attributes变化并更新productAttr
const handleAttributesChange = (row: any, index: number) => {
  console.log(row)

  updateProductAttr(row, index)
}
const updateProductAttr = (row: any, index: number) => {
  // 确保 row 和 attributes 存在
  if (!row || !row.attributes) {
    row.attributes = {}
  }

  // 将非空属性值用"+"号拼接
  row.productAttr = Object.entries(row.attributes)
    .map(([key, value]) => `${key}:${value}`) // 格式为"属性名+属性值"
    .join(',') // 多个属性用逗号分隔

  console.log('Updated productAttr:', row.productAttr)
}
// 删除变体行
const removeVariantRow = (index) => {
  if (form.productVariant.length <= 1) {
    ElMessage.warning('至少需要保留一个变体')
    return
  }
  form.productVariant.splice(index, 1)
}
// 添加属性类型
const addAttributeType = () => {
  if (newAttribute.value.trim()) {
    if (!form.variantAttributes.includes(newAttribute.value.trim())) {
      // 添加新属性类型
      form.variantAttributes.push(newAttribute.value.trim())

      // 为所有现有变体行添加该属性字段
      form.productVariant.forEach((row) => {
        if (!row.attributes[newAttribute.value.trim()]) {
          row.attributes[newAttribute.value.trim()] = ''
        }
      })

      newAttribute.value = ''
      showAttributeInput.value = false
    } else {
      ElMessage.warning('该属性类型已存在')
    }
  }
}

// 删除属性类型
const removeAttributeType = (index: number) => {
  const attrToRemove = form.variantAttributes[index]

  // 从属性类型列表中移除
  form.variantAttributes.splice(index, 1)

  // 从所有变体行的attributes对象中移除该属性
  form.productVariant.forEach((row) => {
    delete row.attributes[attrToRemove]
  })
}
// 处理变体图片上传
const handleVariantImageChange = (file: UploadUserFile, row: any) => {
  // 检查文件类型
  const isImage = file.raw?.type === 'image/jpeg' || file.raw?.type === 'image/png'
  if (!isImage) {
    ElMessage.error('商品图片只能是JPG或PNG格式!')
    return false
  }

  // 检查文件大小
  const isLt5M = file.size! / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('商品图片大小不能超过5MB!')
    return false
  }

  // 检查文件对象
  if (!file.raw) {
    ElMessage.error('无法获取文件对象!')
    return false
  }

  // 保存原始文件对象
  row.variantPic = file.raw

  // 设置预览
  const reader = new FileReader()
  reader.onload = (e: any) => {
    row.image = e.target.result
  }
  reader.readAsDataURL(file.raw)

  console.log('变体图片已保存:', file.raw)
  return false
}

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' },
    { min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur' },
  ],
  category_id: [{ required: true, message: '请选择商品分类', trigger: 'change' }],
  pic: [{ required: true, message: '请上传商品主图', trigger: 'change' }],
  albumPics: [
    {
      validator: (rule, value, callback) => {
        if (!galleryList.value || galleryList.value.length !== 8) {
          callback(new Error('必须上传8张商品展示图'))
        } else {
          callback()
        }
      },
      trigger: 'change',
    },
    {
      validator: (rule, value, callback) => {
        const invalidFiles = galleryList.value.filter((file) => file.size > 5 * 1024 * 1024)
        if (invalidFiles.length > 0) {
          callback(new Error(`每张图片不能超过5MB，当前有${invalidFiles.length}张图片过大`))
        } else {
          callback()
        }
      },
      trigger: 'change',
    },
  ],
  inventory: [{ required: true, message: '请输入商品库存', trigger: 'blur' }],
  detail_html: [{ min: 0, max: 1000, message: '最多1000个字符', trigger: 'blur' }],
  brand_name: [{ required: true, message: '请输入商品品牌名称,若无品牌则填写无', trigger: 'blur' }],
}

// 临时模拟获取分类数据
const fetchCategories = async () => {
  try {
    // 调用真实接口获取分类数据
    const response = await getCategoryTree()
    // 接口返回的数据结构
    categoryOptions.value = response.data
    processThirdLevelCategories(response.data)
  } catch (error) {
    console.error('获取分类数据失败:', error)
    ElMessage.error('获取分类数据失败，请刷新重试')
  }
}

// 处理三级分类数据
const processThirdLevelCategories = (categories: any[]) => {
  const result: { id: number; fullName: string }[] = []

  const traverse = (categories: any[], parentNames: string[] = []) => {
    categories.forEach((category) => {
      const currentPath = [...parentNames, category.name]

      // 如果是三级分类（level为2），则添加到结果数组
      if (category.level === 2) {
        result.push({
          id: category.id,
          fullName: currentPath.join(' > '),
        })
      }

      // 递归遍历子分类
      if (category.children && category.children.length > 0) {
        traverse(category.children, currentPath)
      }
    })
  }

  traverse(categories)
  thirdLevelCategories.value = result
}

// 处理主图上传
const handleMainImageChange = (file: UploadUserFile) => {
  // 检查文件类型
  const isImage = file.raw?.type === 'image/jpeg' || file.raw?.type === 'image/png'
  if (!isImage) {
    ElMessage.error('商品主图只能是JPG或PNG格式!')
    return false
  }

  // 检查文件对象
  if (!file.raw) {
    ElMessage.error('无法获取文件对象!')
    return false
  }

  // 检查文件大小
  const isLt5M = file.size! / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('商品主图大小不能超过5MB!')
    return false
  }

  // 如果已经有主图，先计算减去当前主图后的总大小
  let additionalSize = file.size!
  if (mainPicFile.value) {
    additionalSize = file.size! - mainPicFile.value.size
  }

  // 保存原始文件对象
  mainPicFile.value = file.raw

  // 设置预览
  const reader = new FileReader()
  reader.onload = (e: any) => {
    form.pic = e.target.result
  }
  reader.readAsDataURL(file.raw)

  console.log('主图文件已保存:', file.raw.name, file.raw.size, file.raw.type)
  return false // 阻止自动上传
}

// 处理相册上传
const handleGalleryChange = (file: UploadUserFile, fileList: UploadUserFile[]) => {
  // 检查文件类型和大小
  const isImage = file.raw?.type === 'image/jpeg' || file.raw?.type === 'image/png'
  if (!isImage) {
    ElMessage.error('商品图片只能是JPG或PNG格式!')
    return false
  }

  // 检查单个文件大小
  const isLt5M = file.size! / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('商品图片大小不能超过5MB!')
    return false
  }

  // 限制图片数量
  if (fileList.length > 8) {
    ElMessage.warning('最多只能上传5张商品展示图')
    fileList.pop() // 移除最后一个
    return false
  }

  // 检查文件对象
  if (!file.raw) {
    ElMessage.error('无法获取文件对象!')
    return false
  }

  // 更新相册文件列表
  albumPicFiles.value = []
  fileList.forEach((item) => {
    if (item.raw) {
      albumPicFiles.value.push(item.raw)
    }
  })

  // 更新预览图
  const reader = new FileReader()
  reader.onload = () => {
    // 更新相册预览
    galleryList.value = fileList

    // 更新album_pics字段
    const galleries = fileList
      .map((item) => {
        if (item.url) return item.url
        if (item.raw) {
          const rawFile = item.raw as File
          return URL.createObjectURL(rawFile)
        }
        return ''
      })
      .filter((url) => url !== '')

    form.album_pics = galleries.join(',')
  }
  reader.readAsDataURL(file.raw)

  console.log('相册文件已更新, 当前数量:', albumPicFiles.value.length)

  return false
}

// 处理图文介绍图片上传
const handleIntroductionChange = (file: UploadUserFile, fileList: UploadUserFile[]) => {
  // 检查文件类型和大小
  const isImage = file.raw?.type === 'image/jpeg' || file.raw?.type === 'image/png'
  if (!isImage) {
    ElMessage.error('商品图片只能是JPG或PNG格式!')
    return false
  }

  // 检查单个文件大小
  const isLt5M = file.size! / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('商品图片大小不能超过5MB!')
    return false
  }

  // 限制图片数量
  if (fileList.length > 5) {
    ElMessage.warning('最多只能上传5张商品展示图')
    fileList.pop() // 移除最后一个
    return false
  }

  // 检查文件对象
  if (!file.raw) {
    ElMessage.error('无法获取文件对象!')
    return false
  }

  // 更新文件列表
  graphicIntroductList.value = fileList

  // 更新原始文件列表
  graphicIntroductFiles.value = fileList.map((item) => item.raw).filter(Boolean) as File[]

  return false
}
// 新增beforePdfUpload验证方法
const beforePdfUpload = (file: File) => {
  const isPdf = file.type === 'application/pdf'
  const isLt10M = file.size / 1024 / 1024 < 8

  if (!isPdf) {
    ElMessage.error('只能上传PDF格式的文件!')
    return false
  }

  if (!isLt10M) {
    ElMessage.error('PDF文件大小不能超过8MB!')
    return false
  }

  return true
}

// 修改handlePdfChange方法
const handlePdfChange = (file: UploadUserFile, fileList: UploadUserFile[]) => {
  if (!file.raw) {
    ElMessage.error('无法获取文件对象!')
    return false
  }

  // 验证文件
  if (!beforePdfUpload(file.raw)) {
    return false
  }

  // 添加到文件列表
  pdfFileList.value = fileList
  pdfFiles.value.push(file.raw)
  console.log('当前PDF文件列表:', pdfFiles.value)
  return false
}

// pdf移除
const handlePdfRemove = (file: UploadUserFile, fileList: UploadUserFile[]) => {
  // 从原始文件列表中移除
  const index = pdfFiles.value.findIndex((f) => f.name === file.name)
  if (index !== -1) {
    pdfFiles.value.splice(index, 1)
  }

  // 更新上传列表
  pdfFileList.value = fileList

  console.log('移除后PDF文件列表:', pdfFiles.value)
}
// 处理图文介绍图片移除
const handleIntroductionRemove = (file: UploadUserFile) => {
  const index = graphicIntroductList.value.findIndex((item) => item.uid === file.uid)
  if (index !== -1) {
    graphicIntroductList.value.splice(index, 1)
    graphicIntroductFiles.value = graphicIntroductList.value
      .map((item) => item.raw)
      .filter(Boolean) as File[]
  }
}
// 处理相册图片移除
const handleGalleryRemove = (file: UploadUserFile) => {
  const index = galleryList.value.indexOf(file)
  if (index !== -1) {
    // 更新预览图片
    const galleries = form.album_pics.split(',')
    galleries.splice(index, 1)
    form.album_pics = galleries.join(',')

    // 更新原始文件列表
    albumPicFiles.value.splice(index, 1)
  }
}
// 处理表单提交
const handleSubmit = async () => {
  try {
    submitLoading.value = true
    console.log('===== 开始提交商品表单 =====')

    // 验证表单
    if (!form.name) {
      ElMessage.error('请输入商品名称')
      return
    }

    if (!form.category_id) {
      ElMessage.error('请选择商品分类')
      return
    }
    if (!mainPicFile.value) {
      ElMessage.error('请上传商品主图')
      return
    }
    if (!form.package.weight) {
      ElMessage.error('请输入商品重量')
      return
    }
    if (!form.package.length) {
      ElMessage.error('请输入包裹长度')
      return
    }
    if (!form.package.width) {
      ElMessage.error('请输入包裹宽度')
      return
    }
    if (!form.package.height) {
      ElMessage.error('请输入包裹高度')
      return
    }
    if (albumPicFiles.value.length < 8) {
      ElMessage.error('请上传8张商品相册')
      return
    }

    // PDF验证
    pdfFiles.value.forEach((file, index) => {
      if (file && file.size / 1024 / 1024 > 8) {
        ElMessage.error(`PDF文件大小不能超过8MB!第${index + 1}个文件大小过大`)
        return
      }
    })

    if (form.productVariant.some((item) => item.price === 0 || item.price === null)) {
      ElMessage.error('请为所有商品变体输入商品价格')
      return
    }

    // 检查库存是否输入
    if (form.productVariant.length > 0) {
      const hasInvalidInventory = form.productVariant.some(
        (variant) => variant.inventory === 0 || variant.inventory === null,
      )
      if (hasInvalidInventory) {
        ElMessage.error('请为所有商品变体输入库存数量')
        return
      }
    } else {
      ElMessage.error('请至少添加一个商品变体')
      return
    }

    // 检查是否有变体图片
    const hasNoVariantImage = form.productVariant.some((variant) => !variant.variantPic)
    if (hasNoVariantImage) {
      ElMessage.error('请为所有商品变体上传图片')
      return
    }

    // 获取当前登录商家ID
    const sellerId = userStore.userInfo?.id

    console.log('商家ID:', sellerId)
    if (!sellerId) {
      ElMessage.error('未获取到商家ID，请重新登录')
      return
    }

    // 准备分类名称和品牌名称
    if (form.category_id) {
      const category = thirdLevelCategories.value.find((item) => item.id === form.category_id)
      if (category) {
        form.product_category_name = category.fullName
      }
    }

    if (form.brand_name) {
      const brand = brandOptions.find((item) => item.name === form.brand_name)
      if (brand) {
        form.brand_id = brand.id
      }
    }

    // 创建要传递给后端的商品对象，确保字段名称与后端匹配
    // 重要：严格不包含任何图片相关字段
    const product = {
      productSnapshotId: null, // 新增时为空
      brandId: form.brand_id || null,
      brandName: form.brand_name || '',
      categoryId: form.category_id || null,
      productCategoryName: form.product_category_name || '',
      outProductId: form.out_product_id || '',
      name: form.name,
      publishStatus: form.publish_status,
      sort: form.sort || 0,
      price: form.price || 0,
      unit: form.unit || '',
      enableVariant: form.enableVariant ? 1 : 0,
      packageInfo: {
        weight: form.package.weight + weightUnit.value,
        length: form.package.length,
        width: form.package.width,
        height: form.package.height,
      },
      detailHtml: form.detail_html || '',
      productVariant: form.productVariant.map(({ variantPic, attributes, image, ...rest }) => rest),
      // 不包含pic和albumPics字段
    }
    // 准备变体图片数据
    const variantPics = form.productVariant.map((variant) => variant.variantPic)

    // 详细打印将要提交的商品数据
    console.log('准备调用addProduct API，转换后的商品数据:', {
      name: product.name,
      brandId: product.brandId,
      categoryId: product.categoryId,
      price: product.price,
      publishStatus: product.publishStatus,
    })

    console.log('图片信息（将单独传递）:')
    console.log(
      '- 主图:',
      mainPicFile.value ? `${mainPicFile.value.name}, ${mainPicFile.value.size} 字节` : '未上传',
    )
    console.log('- 相册图片数量:', albumPicFiles.value.length)
    if (albumPicFiles.value.length > 0) {
      console.log('  相册图片明细:')
      albumPicFiles.value.forEach((file, index) => {
        console.log(`  - 相册图${index + 1}: ${file.name}, ${file.size} 字节, 类型: ${file.type}`)
      })
    }

    // 调用API模块中的方法，传递文件对象
    console.log('开始调用API，传递商品数据和图片文件...')
    console.log(
      '传入的图片：',
      mainPicFile.value,
      albumPicFiles.value,
      graphicIntroductFiles.value,
      pdfFiles.value,
      variantPics,
    )

    const result = await addProduct(
      sellerId,
      product,
      mainPicFile.value,
      albumPicFiles.value,
      graphicIntroductFiles.value,
      pdfFiles.value,
      variantPics,
    )
    console.log('addProduct API返回结果:', result)
    if (result.code === 1) {
      // 显示成功消息
      ElMessage.success('商品发布成功')
      console.log('商品添加成功，准备重置表单并跳转')

      console.log('响应消息', result.data)

      // 打开审核弹窗而不是直接跳转
      auditDialogVisible.value = true

      // 保存当前商品名称用于显示在弹窗中
      submittedProductName.value = form.name

      console.log('表单信息：', form)

      // 表单重置
      resetForm()
    } else {
      ElMessage.error(result.msg || '商品发布失败')
      console.error('商品添加失败:', result.msg)
    }
  } catch (error: any) {
    console.error('===== 商品提交失败 =====', error)

    // 分析错误类型并显示更具体的错误信息
    let errorMessage = '商品发布失败'

    if (error.message) {
      errorMessage += `: ${error.message}`
      console.error('错误消息:', error.message)
    }

    if (error.response) {
      console.error('服务器错误响应:', error.response)
      if (error.response.status) {
        errorMessage += ` (状态码: ${error.response.status})`
      }
    }

    ElMessage.error(errorMessage)
  } finally {
    console.log('===== 表单提交处理完成 =====')
    submitLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  form.name = ''
  form.brand_name = ''
  form.category_id = null
  form.product_category_name = ''
  form.price = 0
  form.detail_html = ''
  form.pic = ''
  form.album_pics = ''
  form.publish_status = 1
  form.sort = 0

  // 重置文件
  mainPicFile.value = null
  albumPicFiles.value = []
  galleryList.value = []
  graphicIntroductList.value = []
  // 重置其他状态
  selectedAttrs.value = new Array(predefinedAttrs.length).fill([])

  // 如果表单引用存在，重置验证状态
  formRef.value?.resetFields()

  console.log('表单已重置')
}

// 在分类树中递归查找分类名称
const findCategoryNameById = (categories: any[], categoryId: number): any | null => {
  for (const category of categories) {
    if (category.id === categoryId) {
      return category
    }
    if (category.children && category.children.length > 0) {
      const found = findCategoryNameById(category.children, categoryId)
      if (found) return found
    }
  }
  return null
}

// 取消
const handleCancel = () => {
  ElMessage.warning('正在返回商品列表')
  router.push('/main/product/list')
}

// 商品审核弹窗相关
const auditDialogVisible = ref(false)
const submittedProductName = ref('')

// 关闭审核弹窗，留在添加商品页面
const closeAuditDialog = () => {
  auditDialogVisible.value = false
}

// 前往商品列表页面
const goToProductList = () => {
  router.push('/main/product/list')
}

// 初始化加载分类数据
onMounted(() => {
  console.log('当前登录的用户信息userStore', userStore.userInfo)

  fetchCategories()
})
onUnmounted(() => {})
</script>

<style scoped lang="scss">
.page-container {
  padding: 16px;

  .page-card {
    :deep(.el-card__header) {
      padding: 16px 20px;
    }

    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .page-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .page-actions {
      display: flex;
      gap: 12px;
    }
  }

  .form-section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;

    .section-title {
      margin: 0 0 20px;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      border-left: 4px solid var(--el-color-primary);
      padding-left: 10px;
    }

    &:last-child {
      margin-bottom: 0;
    }
    .weight-input-group {
      display: flex;
      width: 100%;
      :deep(.el-input__wrapper) {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }

      :deep(.el-select .el-input__wrapper) {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-right: 0;
      }
    }
    .auto-generated-id {
      color: #888;
      font-style: italic;
    }

    .variant-actions {
      margin-top: 10px;
      text-align: right;
    }

    /* 调整表格内输入框的样式 */
    .el-table :deep(.el-input-number) {
      width: 100%;
    }

    .el-table :deep(.el-input-number__decrease),
    .el-table :deep(.el-input-number__increase) {
      display: none;
    }
    .variant-attributes {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 4px;

      .attributes-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        h4 {
          margin: 0;
          font-size: 14px;
          color: #606266;
        }
      }

      .attribute-input {
        margin-bottom: 15px;
        display: flex;
        align-items: center;
      }

      .attribute-types {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .el-tag {
          margin-bottom: 5px;
        }
      }

      .no-attributes-tip {
        margin-top: 8px;
        padding: 8px 12px;
        background-color: #f5f7fa;
        border-radius: 4px;
        color: #606266;
        font-size: 13px;
        display: flex;
        align-items: center;

        .el-icon {
          margin-right: 6px;
          color: #909399;
        }
      }

      .attribute-types {
        margin-top: 12px;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }
    }
    .size-management {
      margin: 5px 0;
      padding: 5px;
      background-color: #f8f9fa;
      border-radius: 4px;

      .size-input-group {
        display: flex;
        margin-bottom: 15px;
      }

      .size-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .el-tag {
          margin-bottom: 5px;
        }
      }
    }
    /* 变体图片上传样式 */
    .variant-image-uploader {
      width: 50px;
      height: 50px;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
      display: flex; /* 添加flex布局 */
      align-items: center; /* 垂直居中 */
      justify-content: center; /* 水平居中 */

      &:hover {
        border-color: var(--el-color-primary);
      }

      .variant-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .upload-placeholder {
        display: flex;
        align-items: center; /* 垂直居中 */
        justify-content: center; /* 水平居中 */
        height: 100%;
        width: 100%; /* 确保占满整个区域 */
        color: #8c939d;

        .el-icon {
          font-size: 20px; /* 调整为更合适的尺寸 */
          margin: 0; /* 移除不必要的margin */
        }
      }
    }

    .upload-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 8px;
    }
  }

  .main-image-uploader {
    .main-image {
      width: 200px;
      height: 200px;
      object-fit: cover;
    }

    .upload-placeholder {
      width: 200px;
      height: 200px;
      border: 1px dashed #d9d9d9;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      &:hover {
        border-color: var(--el-color-primary);
        color: var(--el-color-primary);
      }

      .el-icon {
        font-size: 28px;
        color: #8c939d;
        margin-bottom: 8px;
      }

      .upload-text {
        font-size: 14px;
        color: #8c939d;
      }
    }
  }

  .gallery-uploader {
    :deep(.el-upload--picture-card) {
      width: 150px;
      height: 150px;
      line-height: 150px;
    }

    :deep(.el-upload-list--picture-card .el-upload-list__item) {
      width: 150px;
      height: 150px;
    }
  }

  .upload-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 8px;
  }

  // 添加图片总大小提示样式
  .upload-size-info {
    font-size: 12px;
    margin-top: 8px;
    display: flex;
    align-items: center;

    .size-text {
      &.warning {
        color: #e6a23c;
      }

      &.danger {
        color: #f56c6c;
      }

      &.normal {
        color: #67c23a;
      }
    }

    .size-progress {
      margin-left: 10px;
      flex: 1;
      max-width: 200px;
    }
  }

  .attr-container {
    display: flex;
    gap: 20px;
    width: 100%;
    min-height: 400px;
    border-radius: 4px;
    overflow: hidden;

    @media screen and (max-width: 768px) {
      flex-direction: column;
    }
    .attr-selection-panel,
    .attr-preview-panel {
      flex: 1;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    }
  }

  .panel-header {
    padding: 12px 15px;
    font-weight: 600;
    color: #303133;
    background-color: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .help-icon {
      color: #909399;
      cursor: pointer;
      font-size: 15px;

      &:hover {
        color: var(--el-color-primary);
      }
    }

    .attr-count {
      font-size: 12px;
      font-weight: normal;
      color: #606266;

      b {
        color: var(--el-color-primary);
      }
    }
  }

  .attr-tabs {
    flex: 1;
    margin: 0;
    display: flex;

    :deep(.el-tabs__content) {
      padding: 15px;
      flex: 1;
      overflow-y: auto;
    }
  }
  .attr-show {
    margin: 12px 0;
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
  }
  .error-input {
    :deep(.el-input__wrapper) {
      box-shadow: 0 0 0 1px var(--el-color-danger) !important;
    }
  }

  .inventory-show {
    margin-top: 20px;
    display: flex;
    align-items: flex-start;

    span {
      width: 100px;
      font-size: 14px;
      line-height: 32px;
    }

    .el-form-item {
      flex: 1;
      margin-bottom: 0;

      :deep(.el-form-item__error) {
        padding-top: 4px;
      }
    }
  }

  .attr-options {
    max-height: 300px;
    overflow-y: auto;
    padding-right: 5px;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #e0e0e0;
      border-radius: 4px;
    }
  }

  .attr-checkbox {
    margin-right: 20px;
    margin-bottom: 12px;
    transition: all 0.3s;

    &:hover {
      color: var(--el-color-primary);
    }
  }

  .attr-tags-container {
    min-height: 200px;
    border: 1px dashed #ebeef5;
    border-radius: 4px;
    padding: 16px;
    transition: all 0.3s;

    &:hover {
      border-color: var(--el-color-primary-light-5);
    }

    .attr-list {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .attr-item {
        background-color: #f8f9fa;
        border-radius: 6px;
        padding: 12px 16px;
        transition: all 0.3s;
        border-left: 3px solid var(--el-color-primary);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

        &:hover {
          background-color: #edf2fc;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .attr-content {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .attr-value {
            font-weight: 500;
            color: #303133;
            font-size: 14px;
          }

          .attr-meta {
            display: flex;
            align-items: center;
            gap: 16px;

            .inventory {
              display: inline-flex;
              align-items: center;
              gap: 4px;
              font-size: 13px;
              color: #606266;

              .el-icon {
                color: var(--el-color-primary);
                font-size: 14px;
              }
            }

            .attr-actions {
              .el-button {
                .el-icon {
                  font-size: 14px; // 确保图标大小可见
                }
              }
            }
          }
        }

        &:hover .attr-actions {
          opacity: 1;
        }
      }
    }

    .no-attrs {
      .empty-tip {
        color: #909399;
        margin-bottom: 4px;
      }

      .empty-tip-sub {
        color: #c0c4cc;
        font-size: 12px;
      }
    }
  }
}

// 响应式调整
@media screen and (max-width: 768px) {
  .page-container {
    .form-section {
      padding: 15px;
    }

    :deep(.el-form-item) {
      margin-bottom: 15px;
    }
  }
}
.editor {
  display: flex;
  flex-direction: column;
}
</style>
