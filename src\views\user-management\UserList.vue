<template>
  <div class="user-list-container">
    <div class="page-header">
      <h2>用户列表</h2>
      <p>查看并管理所有注册用户的基本信息</p>
    </div>

    <el-card class="search-card">
      <div class="search-container">
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索用户名、手机号或邮箱"
            prefix-icon="Search"
            clearable
            @keyup.enter="loadUserList"
            @clear="loadUserList"
          ></el-input>
          <el-button type="primary" @click="loadUserList">搜索</el-button>
        </div>

        <div class="date-range-box">
          <el-select v-model="dateRange" placeholder="时间范围" @change="loadUserList">
            <el-option label="全部时间" value="all"></el-option>
            <el-option label="最近7天" value="last7days"></el-option>
            <el-option label="最近14天" value="last14days"></el-option>
            <el-option label="最近30天" value="last30days"></el-option>
            <el-option label="本周" value="thisWeek"></el-option>
            <el-option label="本月" value="thisMonth"></el-option>
            <el-option label="自定义" value="custom"></el-option>
          </el-select>

          <el-date-picker
            v-if="dateRange === 'custom'"
            v-model="customDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            @change="loadUserList"
          ></el-date-picker>
        </div>
      </div>
    </el-card>

    <el-card class="table-card">
      <div class="user-stats">
        <div class="stat-item">
          <h3>{{ userStats.totalUsers }}</h3>
          <p>总用户数</p>
        </div>
        <div class="stat-item">
          <h3>{{ userStats.newUsersToday }}</h3>
          <p>今日新增</p>
        </div>
        <div class="stat-item">
          <h3>{{ userStats.activeUsers }}</h3>
          <p>活跃用户</p>
        </div>
        <div class="stat-item">
          <h3>{{ userStats.averagePurchase }}</h3>
          <p>平均购买价值</p>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="userList"
        border
        style="width: 100%"
        @row-click="handleRowClick"
      >
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="username" label="用户名"></el-table-column>
        <el-table-column prop="phone" label="手机号"></el-table-column>
        <el-table-column prop="email" label="邮箱"></el-table-column>
        <el-table-column prop="registerTime" label="注册时间" width="180"></el-table-column>
        <el-table-column prop="lastLoginTime" label="最近登录" width="180"></el-table-column>
        <el-table-column prop="orderCount" label="订单数" width="100"></el-table-column>
        <el-table-column prop="totalSpent" label="消费金额" width="120">
          <template #default="scope">
            <span>¥{{ scope.row.totalSpent.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="180">
          <template #default="scope">
            <el-button size="small" type="primary" @click.stop="viewUserDetail(scope.row.id)">
              详情
            </el-button>
            <el-button size="small" type="success" @click.stop="viewUserPurchases(scope.row.id)">
              购买记录
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'

defineOptions({
  name: 'UserList',
})

const router = useRouter()

// 搜索和筛选
const searchKeyword = ref('')
const dateRange = ref('all')
const customDateRange = ref([])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)

// 数据
const loading = ref(false)
const userList = ref([])
const userStats = reactive({
  totalUsers: 0,
  newUsersToday: 0,
  activeUsers: 0,
  averagePurchase: 0,
})

// 加载用户列表
const loadUserList = async () => {
  loading.value = true
  try {
    // const res = await getUserList({
    //   page: currentPage.value,
    //   pageSize: pageSize.value,
    //   keyword: searchKeyword.value,
    //   dateRange: dateRange.value,
    //   startDate: customDateRange.value?.[0],
    //   endDate: customDateRange.value?.[1],
    // })

    // 模拟数据
    await new Promise((resolve) => setTimeout(resolve, 500))

    // 用户统计信息
    userStats.totalUsers = 1258
    userStats.newUsersToday = 42
    userStats.activeUsers = 785
    userStats.averagePurchase = 521.35

    // 用户列表
    userList.value = [
      {
        id: 1,
        username: '张三',
        phone: '13800138001',
        email: '<EMAIL>',
        registerTime: '2023-01-15 10:32:41',
        lastLoginTime: '2023-04-08 08:45:22',
        orderCount: 12,
        totalSpent: 2845.5,
      },
      {
        id: 2,
        username: '李四',
        phone: '13900139002',
        email: '<EMAIL>',
        registerTime: '2023-02-05 14:22:18',
        lastLoginTime: '2023-04-07 15:12:33',
        orderCount: 8,
        totalSpent: 1678.25,
      },
      {
        id: 3,
        username: '王五',
        phone: '13700137003',
        email: '<EMAIL>',
        registerTime: '2023-02-18 09:15:37',
        lastLoginTime: '2023-04-06 19:08:55',
        orderCount: 5,
        totalSpent: 1245.8,
      },
      {
        id: 4,
        username: '赵六',
        phone: '13600136004',
        email: '<EMAIL>',
        registerTime: '2023-03-02 16:48:22',
        lastLoginTime: '2023-04-08 11:32:47',
        orderCount: 3,
        totalSpent: 892.6,
      },
      {
        id: 5,
        username: '钱七',
        phone: '13500135005',
        email: '<EMAIL>',
        registerTime: '2023-03-15 11:27:59',
        lastLoginTime: '2023-04-05 14:29:18',
        orderCount: 2,
        totalSpent: 499.0,
      },
      {
        id: 6,
        username: '孙八',
        phone: '13400134006',
        email: '<EMAIL>',
        registerTime: '2023-03-22 13:41:05',
        lastLoginTime: '2023-04-04 17:52:34',
        orderCount: 1,
        totalSpent: 199.99,
      },
      {
        id: 7,
        username: '周九',
        phone: '13300133007',
        email: '<EMAIL>',
        registerTime: '2023-03-28 09:08:45',
        lastLoginTime: '2023-04-07 09:15:29',
        orderCount: 1,
        totalSpent: 299.5,
      },
      {
        id: 8,
        username: '吴十',
        phone: '13200132008',
        email: '<EMAIL>',
        registerTime: '2023-04-01 15:33:27',
        lastLoginTime: '2023-04-08 10:42:16',
        orderCount: 0,
        totalSpent: 0.0,
      },
      {
        id: 9,
        username: '郑十一',
        phone: '13100131009',
        email: '<EMAIL>',
        registerTime: '2023-04-03 16:22:18',
        lastLoginTime: '2023-04-08 09:28:51',
        orderCount: 0,
        totalSpent: 0.0,
      },
      {
        id: 10,
        username: '王十二',
        phone: '13000130010',
        email: '<EMAIL>',
        registerTime: '2023-04-05 14:15:37',
        lastLoginTime: '2023-04-06 18:42:09',
        orderCount: 0,
        totalSpent: 0.0,
      },
    ]

    totalCount.value = 1258 // 模拟总用户数
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 处理分页大小变更
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadUserList()
}

// 处理当前页变更
const handleCurrentChange = (page) => {
  currentPage.value = page
  loadUserList()
}

// 查看用户详情
const viewUserDetail = (id) => {
  router.push(`/main/user-management/user-detail/${id}`)
}

// 查看用户购买记录
const viewUserPurchases = (id) => {
  router.push(`/main/user-management/user-detail/${id}?tab=purchases`)
}

// 行点击事件
const handleRowClick = (row) => {
  viewUserDetail(row.id)
}

// 初始化加载数据
onMounted(() => {
  loadUserList()
})
</script>

<style scoped lang="scss">
.user-list-container {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;

    h2 {
      font-size: 24px;
      margin-bottom: 8px;
    }

    p {
      color: #606266;
      font-size: 14px;
    }
  }

  .search-card {
    margin-bottom: 20px;
  }

  .search-container {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 15px;

    .search-box {
      display: flex;
      gap: 10px;
      width: 400px;
    }

    .date-range-box {
      display: flex;
      gap: 10px;
      align-items: center;
    }
  }

  .table-card {
    margin-bottom: 20px;
  }

  .user-stats {
    display: flex;
    margin-bottom: 20px;
    gap: 20px;

    .stat-item {
      background: #f8f9fb;
      border-radius: 6px;
      padding: 15px 20px;
      flex: 1;
      text-align: center;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      h3 {
        font-size: 22px;
        margin-bottom: 5px;
        font-weight: 600;
        color: #409eff;
      }

      p {
        color: #606266;
        margin: 0;
        font-size: 14px;
      }

      &:nth-child(2) h3 {
        color: #67c23a;
      }

      &:nth-child(3) h3 {
        color: #e6a23c;
      }

      &:nth-child(4) h3 {
        color: #f56c6c;
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }

  @media screen and (max-width: 768px) {
    .search-container {
      flex-direction: column;

      .search-box,
      .date-range-box {
        width: 100%;
      }
    }

    .user-stats {
      flex-wrap: wrap;

      .stat-item {
        min-width: calc(50% - 10px);
        margin-bottom: 10px;
      }
    }
  }
}
</style>
