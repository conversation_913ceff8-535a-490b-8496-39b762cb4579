import request from '@/utils/request'
import axios from 'axios'

/**
 * 获取商品列表
 * @param params 查询参数
 * @returns 商品列表数据
 */
export function getProductList(params: any) {
  return request({
    url: '/products',
    method: 'get',
    params,
  })
}

/**
 * 获取指定商家的商品列表
 * @param sellerId 商家ID
 * @param params 查询参数
 * @returns 商品列表数据
 */
export function getSellerProductList(sellerId: number, params: any = {}) {
  return request({
    url: `/products/product/seller/${sellerId}`,
    method: 'get',
    params,
  })
}

/**
 * 获取商家的商品详情信息
 * @param sellerId 商家ID
 * @param productId 商品ID
 * @returns 商品详情数据
 */
export function getSellerProductDetail(sellerId: number, productId: number) {
  return request({
    url: `/products/product/seller/${sellerId}/${productId}`,
    method: 'get',
  })
}

// 获取商品详情
export function getProductDetail(id: number) {
  return request({
    url: `/products/${id}`,
    method: 'get',
  })
}

/**
 * 新增商品
 * @param sellerId 商家ID
 * @param product 商品数据
 * @param mainPic 主图文件
 * @param albumPics 相册图片文件数组
 */
export async function addProduct(
  sellerId: number,
  product: any,
  mainPic: File,
  albumPics: File[],
  introductPics?: File[],
  pdfDocument?: File[],
  variantPics?: File[],
): Promise<any> {
  try {
    console.log('===== addProduct API调用开始 =====')
    console.log('商家ID:', sellerId)
    console.log('商品数据详情:', {
      name: product.name,
      brandId: product.brandId,
      brandName: product.brandName,
      categoryId: product.categoryId,
      productCategoryName: product.productCategoryName,
      outProductId: product.outProductId,
      price: product.price,
      unit: product.unit,
      weight: product.weight,
      publishStatus: product.publishStatus,
      sort: product.sort,
      detailHtml:
        product.detailHtml?.substr(0, 50) + (product.detailHtml?.length > 50 ? '...' : ''),
    })

    // 检查关键字段
    if (!product.brandName && product.brandId) {
      console.warn('警告: 品牌ID存在但品牌名称为空')
    }

    if (!product.productCategoryName && product.categoryId) {
      console.warn('警告: 分类ID存在但分类名称为空')
    }

    // 构建完整URL
    const baseURL = import.meta.env.VITE_API_BASE_URL || ''
    const url = `${baseURL}/products/seller/${sellerId}`
    console.log('请求URL:', url)

    // 创建FormData对象
    const formData = new FormData()

    try {
      // 确保清理对象中可能存在的pic和albumPics属性
      const cleanProduct = { ...product }
      delete cleanProduct.pic
      delete cleanProduct.albumPics
      delete cleanProduct.album_pics
      delete cleanProduct.pdfDocument

      // 关键点：使用Blob封装JSON对象，并设置正确的Content-Type
      const productJson = JSON.stringify(cleanProduct)
      console.log('JSON字符串长度:', productJson.length)
      console.log(
        'JSON字符串预览:',
        productJson.substring(0, 200) + (productJson.length > 200 ? '...' : ''),
      )

      formData.append(
        'pmsProduct',
        new Blob([productJson], {
          type: 'application/json',
        }),
      )
      console.log('已添加商品数据到FormData')
    } catch (err) {
      console.error('添加product到FormData失败:', err)
      throw new Error('构建请求数据失败')
    }

    // 添加主图
    try {
      console.log('添加主图:', mainPic.name, `类型:${mainPic.type}`, `大小:${mainPic.size} 字节`)
      formData.append('pic', mainPic)
      console.log('已添加主图到FormData')
    } catch (err) {
      console.error('添加主图到FormData失败:', err)
      throw new Error('添加图片失败')
    }

    // 添加相册图片
    if (albumPics && albumPics.length > 0) {
      try {
        console.log('添加相册图片:', albumPics.length, '张')
        albumPics.forEach((file, index) => {
          console.log(
            `- 相册图${index + 1}:`,
            file.name,
            `类型:${file.type}`,
            `大小:${file.size} 字节`,
          )
          formData.append('albumPics', file)
        })
        console.log('已添加相册图片到FormData')
      } catch (err) {
        console.error('添加相册图片到FormData失败:', err)
        throw new Error('添加相册图片失败')
      }
    } else {
      console.log('没有相册图片需要上传')
    }

    // 添加图文介绍图片（与相册图片逻辑相同）
    if (introductPics && introductPics.length > 0) {
      try {
        console.log('添加图文介绍图片:', introductPics.length, '张')
        introductPics.forEach((file, index) => {
          console.log(
            `- 介绍图${index + 1}:`,
            file.name,
            `类型:${file.type}`,
            `大小:${file.size} 字节`,
          )
          formData.append('introductPics', file) // 字段名设为 introductPics
        })
        console.log('已添加图文介绍图片到FormData')
      } catch (err) {
        console.error('添加图文介绍图片到FormData失败:', err)
        throw new Error('添加图文介绍图片失败')
      }
    } else {
      console.log('没有图文介绍图片需要上传')
    }
    // 添加pdf文档
    if (pdfDocument) {
      try {
        pdfDocument.forEach((file, index) => {
          console.log(
            `- PDF文档${index + 1}:`,
            file.name,
            `类型:${file.type}`,
            `大小:${file.size} 字节`,
          )
          formData.append('pdfDocument', file)
        })
        console.log('已添加PDF文档到FormData')
      } catch (err) {
        console.error('添加PDF文档到FormData失败:', err)
        throw new Error('添加PDF文档失败')
      }
    } else {
      console.log('没有PDF文档需要上传')
    }
    // 添加变体图片
    if (variantPics && variantPics.length > 0) {
      try {
        console.log('添加变体图片:', variantPics.length, '张')
        variantPics.forEach((file, index) => {
          console.log(
            `- 变体图${index + 1}:`,
            file.name,
            `类型:${file.type}`,
            `大小:${file.size} 字节`,
          )
          formData.append('variantPics', file) // 使用统一的字段名
        })
        console.log('已添加变体图片到FormData')
      } catch (err) {
        console.error('添加变体图片到FormData失败:', err)
        throw new Error('添加变体图片失败')
      }
    } else {
      console.log('没有变体图片需要上传')
    }

    // 检查并记录FormData内容
    console.log('FormData内容检查:')
    try {
      for (const pair of formData.entries()) {
        const value = pair[1]
        const isFile = typeof value === 'object' && value !== null && 'name' in value
        const isBlob = value instanceof Blob

        if (isFile && 'name' in value) {
          console.log(
            `- ${pair[0]}: [File] ${(value as any).name}, 大小: ${(value as any).size} 字节`,
          )
        } else if (isBlob) {
          console.log(`- ${pair[0]}: [Blob] 大小: ${(value as Blob).size} 字节`)
        } else {
          console.log(`- ${pair[0]}: ${value}`)
        }
      }
    } catch (err) {
      console.warn('FormData内容检查失败:', err)
    }

    // 获取认证token
    const token = localStorage.getItem('sharewharf_token')
    console.log('认证Token:', token ? '已获取' : '未获取')

    // 发送请求 - 注意：不要手动设置Content-Type，让浏览器自动处理
    console.log('===== 发送multipart/form-data请求 =====')
    console.time('请求耗时')

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          Authorization: token ? `${token}` : '',
          // 不要手动设置Content-Type，浏览器会自动添加正确的boundary
        },
        body: formData,
      })
      console.timeEnd('请求耗时')
      console.log('请求已发送，状态码:', response.status, response.statusText)

      // 检查响应
      if (!response.ok) {
        const errorText = await response.text()
        console.error(`请求失败 (${response.status}): ${errorText}`)
        throw new Error(`请求失败: ${response.status} ${response.statusText} ${errorText}`)
      }

      // 解析响应
      const result = await response.json()
      console.log('商品添加成功，响应:', result)
      return result
    } catch (err) {
      console.error('请求发送或解析响应时出错:', err)
      throw err
    }
  } catch (error) {
    console.error('===== 添加商品失败 =====', error)
    return {
      code: 0,
      msg: error instanceof Error ? error.message : '添加商品失败',
      data: null,
    }
  }
}

/**
 * 文件转Base64
 */
function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = (error) => reject(error)
  })
}

// 更新商品
export function updateProduct(id: number, data: any) {
  return request({
    url: `/products/${id}`,
    method: 'put',
    data,
  })
}

/**
 * 商家更新商品
 * @param sellerId 商家ID
 * @param productId 商品ID
 * @param product 商品数据
 * @param mainPic 主图文件
 * @param albumPics 相册图片文件数组
 */
export async function updateSellerProduct(
  sellerId: number,
  productId: number,
  product: any,
  mainPic: File | null,
  albumPics: File[] = [],
  introductPics?: File[],
  pdfDocument?: File,
  variantPics?: File[],
): Promise<any> {
  try {
    console.log('===== 开始商品更新请求 =====')
    console.log('商家ID:', sellerId, 'productId:', productId)

    // 构建完整URL
    const baseURL = import.meta.env.VITE_API_BASE_URL || ''
    const url = `${baseURL}/products/seller/${sellerId}/${productId}`
    console.log('请求URL:', url)

    // 检查输入对象，查看是否包含图片字段
    console.log('输入的product对象检查:')
    console.log('- pic属性:', 'pic' in product ? '存在' : '不存在')
    console.log('- albumPics属性:', 'albumPics' in product ? '存在' : '不存在')
    console.log('- album_pics属性:', 'album_pics' in product ? '存在' : '不存在')

    // 存储原始主图URL，以便在没有新上传主图时使用
    const originalPicUrl = product.pic

    // 确保product对象中不包含pic和albumPics
    const cleanProduct = { ...product }

    // 明确删除这些属性，确保它们不会出现在product对象中
    delete cleanProduct.pic
    delete cleanProduct.albumPics
    delete cleanProduct.album_pics
    delete cleanProduct.pdfDocument

    console.log('清理后的商品数据 (确认不含图片):', cleanProduct)
    const productJson = JSON.stringify(cleanProduct)
    console.log('商品JSON字符串预览:', productJson.substring(0, 100), '...')

    // 创建FormData对象
    const formData = new FormData()

    // 将商品数据转换为JSON，并添加到FormData
    // 使用正确的参数名 'product'
    formData.append(
      'product',
      new Blob([productJson], {
        type: 'application/json',
      }),
    )

    // 添加主图作为单独的参数 'pic'
    if (mainPic) {
      console.log('添加主图文件:', mainPic.name, '大小:', (mainPic.size / 1024).toFixed(2), 'KB')
      formData.append('pic', mainPic)
    } else if (originalPicUrl) {
      console.log('未上传新主图，传递原始主图URL:', originalPicUrl)
      // 添加原始主图URL作为表单字段，以便后端可以保留原始图片
      formData.append('picUrl', originalPicUrl)
    } else {
      console.log('未上传新主图，且没有原始主图URL')
    }

    // 添加相册图片作为单独的参数 'albumPics'
    if (albumPics && albumPics.length > 0) {
      console.log('添加相册图片:', albumPics.length, '张')
      albumPics.forEach((file, index) => {
        console.log(
          `- 相册图${index + 1}:`,
          file.name,
          '大小:',
          (file.size / 1024).toFixed(2),
          'KB',
        )
        formData.append('albumPics', file)
      })
    }
    // 添加图文介绍图片
    if (introductPics && introductPics.length > 0) {
      try {
        console.log('添加图文介绍图片:', introductPics.length, '张')
        introductPics.forEach((file, index) => {
          console.log(
            `- 介绍图${index + 1}:`,
            file.name,
            `类型:${file.type}`,
            `大小:${file.size} 字节`,
          )
          formData.append('introductPics', file) // 字段名设为 introductPics
        })
        console.log('已添加图文介绍图片到FormData')
      } catch (err) {
        console.error('添加图文介绍图片到FormData失败:', err)
        throw new Error('添加图文介绍图片失败')
      }
    } else {
      console.log('没有图文介绍图片需要上传')
    }
    // 添加pdf文档
    if (pdfDocument) {
      try {
        console.log('添加PDF文档:', pdfDocument.name, `大小:${pdfDocument.size} 字节`)
        formData.append('pdfDocument', pdfDocument)
        console.log('已添加PDF文档到FormData')
      } catch (err) {
        console.error('添加PDF文档到FormData失败:', err)
        throw new Error('添加PDF文档失败')
      }
    } else {
      console.log('没有PDF文档需要上传')
    }
    console.log(variantPics);
    
    // 添加变体图片
    // if (variantPics && variantPics.length > 0) {
    //     console.log('添加变体图片:', variantPics.length, '张')
    //     variantPics.forEach((file, index) => {
    //       console.log(
    //         `- 变体图${index + 1}:`,
    //         file.name,
    //         `类型:${file.type}`,
    //         `大小:${file.size} 字节`,
    //       )
    //       formData.append('variantPics', file) // 使用统一的字段名
    //     })
    //     console.log('已添加变体图片到FormData')
    // } else {
    //   console.log('没有变体图片需要上传')
    // }

    // 日志记录FormData内容
    console.log('FormData字段:')
    for (const key of formData.keys()) {
      console.log(`- ${key}`)
    }

    // 获取认证token
    const token = localStorage.getItem('sharewharf_token')

    // 发送请求 - 不设置Content-Type，让浏览器自动处理
    console.log('发送multipart/form-data请求...')

    // 调试信息
    console.time('请求耗时')

    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        Authorization: token ? `Bearer ${token}` : '',
      },
      body: formData,
    })

    console.timeEnd('请求耗时')
    console.log('服务器响应状态:', response.status, response.statusText)

    // 检查响应
    if (!response.ok) {
      const errorText = await response.text()
      console.error(`请求失败 (${response.status}): ${errorText}`)
      throw new Error(`请求失败: ${response.status} ${response.statusText}`)
    }

    // 解析响应
    const result = await response.json()
    console.log('商品更新成功，响应:', result)
    console.log('===== 商品更新请求结束 =====')
    return result
  } catch (error: any) {
    console.error('更新商品失败:', error)
    throw error
  }
}

// 删除商品
export function deleteProduct(id: number) {
  return request({
    url: `/products/${id}`,
    method: 'delete',
  })
}

/**
 * 删除商家商品
 * @param sellerId 商家ID
 * @param productId 商品ID
 * @returns 删除结果
 */
export async function deleteSellerProduct(sellerId: number, productId: number): Promise<any> {
  try {
    console.log('准备删除商品，sellerId:', sellerId, 'productId:', productId)

    // 构建完整URL
    const baseURL = import.meta.env.VITE_API_BASE_URL || ''
    const url = `${baseURL}/products/seller/${sellerId}/${productId}`
    console.log('删除请求URL:', url)

    // 获取认证token
    const token = localStorage.getItem('token')

    // 发送请求
    const response = await fetch(url, {
      method: 'DELETE',
      headers: {
        Authorization: token ? `Bearer ${token}` : '',
        'Content-Type': 'application/json',
      },
    })

    // 检查响应
    if (!response.ok) {
      const errorText = await response.text()
      console.error(`删除请求失败 (${response.status}): ${errorText}`)
      throw new Error(`删除请求失败: ${response.status} ${response.statusText}`)
    }

    // 解析响应
    const result = await response.json()
    console.log('商品删除成功，响应:', result)
    return result
  } catch (error) {
    console.error('删除商品失败:', error)
    throw error
  }
}

// 更新商品上架状态
export function updateProductStatus(id: number, status: number) {
  return request({
    url: `/products/${id}/status`,
    method: 'put',
    data: { status },
  })
}

/**
 * 获取待审核商品列表
 * @returns 待审核商品列表
 */
export function getProductAuditList() {
  return request({
    url: '/products/audit',
    method: 'get',
  })
}

/**
 * 超级管理员查询所有产品
 * @returns 所有商品列表
 */
export function getAllProductsAdmin() {
  return request({
    url: '/products/admin',
    method: 'get',
  })
}

/**
 * 更新商品审核状态
 * @param productId 商品ID
 * @param auditStatus 审核状态 (1-通过, 2-拒绝)
 * @param remark 审核备注
 * @returns 更新结果
 */
export function updateProductAuditStatus(productId: number, auditStatus: number, remark: string) {
  return request({
    url: `/products/${productId}/audit`,
    method: 'put',
    data: { auditStatus, remark },
  })
}

/**
 * 审核通过商品
 * @param productId 商品ID
 * @returns 处理结果
 */
export function approveProduct(productId: number) {
  return request({
    url: `/products/admin/changePass/${productId}`,
    method: 'put',
  })
}

export interface SupplementNotificationRequest {
  // 邮件标题
  subject:string;
  /** 商品ID */
  productId: number;

  /** 商家用户ID */
  merchantId: number;
  // 审核员ID
  auditorsID:number;
  // 商家邮箱
  email:string;
  /** 补充审核原因 */
  reason: string;

  /** 详细原因 */
  detailedReasons: string;
  // 审核时间
  reviewTime:string;
}

/**
 * 要求商家补充材料
 * @param params 补充材料通知参数
 * @returns 处理结果
 */
export function requestSupplement(data: SupplementNotificationRequest) {
  return request({
    url: '/products/admin/supplement',
    method: 'post',
    data
  })
}

/**
 * 审核不通过商品（删除商品）
 * @param productId 商品ID
 * @returns 处理结果
 */
export function rejectProduct(productId: number) {
  return request({
    url: `/products/admin/changeDelete/${productId}`,
    method: 'put',
  })
}

/**
 * 获取单个商品详情
 * @param productId 商品ID
 * @returns 商品详情
 */
export function getProductById(productId: number) {
  return request({
    url: `/products/${productId}`,
    method: 'get',
  })
}
