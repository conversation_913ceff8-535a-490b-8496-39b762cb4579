<template>
  <div class="page-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="page-header">
          <h2 class="page-title">商品审核管理</h2>
          <div class="page-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索商品名称/品牌"
              class="search-input"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select
              v-model="statusFilter"
              placeholder="审核状态"
              clearable
              @change="handleSearch"
            >
              <el-option label="全部" :value="null" />
              <el-option label="待审核" :value="0" />
              <el-option label="已通过" :value="1" />
            </el-select>
            <el-button type="primary" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <div class="page-content">
        <!-- 批量操作按钮 -->
        <div class="batch-actions" v-if="multipleSelection.length > 0">
          <el-divider content-position="left">已选择 {{ multipleSelection.length }} 项</el-divider>
          <el-button-group>
            <el-button type="success" @click="batchApprove" :disabled="!hasPendingProducts">
              <el-icon><Check /></el-icon> 批量审核通过
            </el-button>
            <el-button type="danger" @click="batchReject" :disabled="!hasPendingProducts">
              <el-icon><Close /></el-icon> 批量审核拒绝
            </el-button>
          </el-button-group>
        </div>

        <!-- 商品列表 -->
        <el-table
          v-loading="loading"
          :data="filteredProducts"
          style="width: 100%"
          border
          @selection-change="handleSelectionChange"
          row-key="id"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column type="index" width="50" label="#" align="center" />
          <el-table-column prop="id" label="商品ID" width="80" align="center" />
          <el-table-column label="商品图片" width="100" align="center">
            <template #default="{ row }">
              <el-image
                :src="
                  row.pic || 'https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png'
                "
                :preview-src-list="row.pic ? [row.pic] : []"
                :preview-teleported="true"
                fit="cover"
                style="width: 80px; height: 80px; border-radius: 4px"
              />
            </template>
          </el-table-column>
          <el-table-column
            prop="name"
            label="商品名称"
            min-width="180"
            show-overflow-tooltip
            :formatter="(row: any) => row.name || '-'"
          />
          <el-table-column
            prop="brandName"
            label="品牌"
            width="120"
            show-overflow-tooltip
            :formatter="(row: any) => row.name || '-'"
          />
          <el-table-column
            prop="productCategoryName"
            label="分类"
            width="120"
            show-overflow-tooltip
            :formatter="(row: any) => row.name || '-'"
          />
          <el-table-column prop="price" label="价格" width="90" align="center">
            <template #default="{ row }">
              <span v-if="row.price > 0" class="price">¥{{ formatPrice(row.price) }}</span>
              <span v-else class="price">¥{{ formatPrice(row.productVariant[0].price) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="审核状态" width="90" align="center">
            <template #default="{ row }">
              <el-tag :type="auditStatusTagType(row.status)" effect="light" size="small">
                {{ auditStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="审核操作" width="250" align="center">
            <template #default="{ row }">
              <el-button-group>
                <el-button size="small" type="primary" @click="handleView(row)">
                  <el-icon><View /></el-icon>
                  查看详情
                </el-button>
                <template v-if="row.status === 0">
                  <el-button
                    size="small"
                    type="success"
                    @click="handleApprove(row)"
                    :title="'通过商品审核'"
                  >
                    <el-icon><Check /></el-icon>
                  </el-button>
                  <el-button
                    size="small"
                    type="warning"
                    @click="handleSupplement(row)"
                    :title="'要求补充材料'"
                  >
                    <el-icon><Refresh /></el-icon>
                  </el-button>
                  <el-button
                    size="small"
                    type="danger"
                    @click="handleReject(row)"
                    :title="'拒绝商品审核'"
                  >
                    <el-icon><Close /></el-icon>
                  </el-button>
                </template>
              </el-button-group>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="160" align="center">
            <template #default="{ row }">
              {{ formatDate(row.createTime) }}
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalItems"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 商品详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="商品详情" width="70%">
      <div v-if="currentProduct" class="product-detail">
        <div class="detail-header">
          <el-image
            :src="
              currentProduct.pic ||
              'https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png'
            "
            fit="cover"
            class="product-image"
          />
          <div class="header-info">
            <h3 class="product-name">{{ currentProduct.name }}</h3>
            <div class="product-meta">
              <div class="meta-item">
                <span class="label">商品ID:</span>
                <span class="value">{{ currentProduct.id }}</span>
              </div>
              <div class="meta-item">
                <span class="label">价格:</span>
                <!-- 兼容无变体的旧商品价格 -->
                <span v-if="currentProduct.price > 0" class="value price"
                  >¥{{ formatPrice(currentProduct.price) }}</span
                >
                <!-- 展示第一个变体的价格 -->
                <span v-else class="value price"
                  >¥{{ formatPrice(currentProduct.productVariant[0].price) }}</span
                >
              </div>
              <div class="meta-item">
                <span class="label">品牌:</span>
                <span class="value">{{ currentProduct.brandName }}</span>
              </div>
              <div class="meta-item">
                <span class="label">分类:</span>
                <span class="value">{{ currentProduct.productCategoryName }}</span>
              </div>
              <div class="meta-item">
                <span class="label">库存:</span>
                <span v-if="currentProduct.inventory > 0" class="value">{{ currentProduct.inventory }}</span>
                <!-- 展示第一个变体的商品库存 -->
                <span v-else class="value">{{
                  currentProduct.productVariant[0].inventory
                }}</span>
              </div>
              <div class="meta-item">
                <span class="label">审核状态:</span>
                <el-tag
                  :type="auditStatusTagType(currentProduct.status)"
                  effect="light"
                  size="small"
                >
                  {{ auditStatusText(currentProduct.status) }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>

        <el-divider />

        <div class="detail-section">
          <h4 class="section-title">商品规格</h4>
          <div v-if="currentProduct.productAttr" class="attribute-list">
            <div
              class="attribute-item"
              v-for="attr in parseProductAttr(currentProduct.productAttr)"
              :key="attr.key"
            >
              <span class="attr-name">{{ attr.key }}:</span>
              <span class="attr-value">{{
                (Array.isArray(attr.value) ? attr.value : [attr.value]).join('、')
              }}</span>
            </div>
          </div>
          <div class="product-variant" v-if="currentProduct.productVariant.length > 0">
            <div class="variant-list">
              <div
                v-for="(variant, index) in currentProduct.productVariant"
                :key="index"
                class="variant-item"
              >
                <!-- 变体图片 -->
                <div class="variant-image">
                  <el-image
                    v-if="variant.variantPic"
                    :src="variant.variantPic"
                    fit="cover"
                    class="variant-pic"
                    :preview-src-list="[variant.variantPic]"
                    :preview-teleported="true"
                    style="width: 60px; height: 60px; border-radius: 4px"
                  />
                  <div v-else class="variant-pic-placeholder">暂无图片</div>
                </div>

                <!-- 变体属性（productAttr） -->
                <div class="variant-attrs">
                  <strong>属性:</strong>
                  <span>
                    {{ variant.productAttr }}
                  </span>
                </div>
                <!-- 尺寸 -->
                <div class="variant-size">
                  <strong>尺寸:</strong>
                  <span>{{
                    Array.isArray(variant.size) ? variant.size.join(', ') : variant.size || '-'
                  }}</span>
                </div>

                <!-- SKU -->
                <div class="variant-sku"><strong>SKU:</strong> {{ variant.sellerSKU || '-' }}</div>

                <!-- 价格 -->
                <div class="variant-price">
                  <strong>价格:</strong> ¥{{ formatPrice(variant.price) }}
                </div>

                <!-- 库存 -->
                <div class="variant-inventory">
                  <strong>库存:</strong> {{ variant.inventory || 0 }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="detail-section" v-if="currentProduct.albumPics">
          <h4 class="section-title">商品相册</h4>
          <div class="album-pics">
            <div
              v-for="(pic, index) in parseAlbumPics(currentProduct.albumPics)"
              class="album-pic-wrapper"
            >
              <el-image
                :key="index"
                :src="pic"
                fit="cover"
                class="album-pic"
                :infinite="false"
                hide-on-click-modal
                preview-teleported
                :z-index="9999"
                :preview-src-list="parseAlbumPics(currentProduct.albumPics)"
                :initial-index="index"
              />
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4 class="section-title">商品描述</h4>
          <div class="product-description" v-html="currentProduct.detailHtml"></div>
        </div>
      </div>

      <!-- 图文介绍部分 -->
      <div class="detail-section">
        <h4 class="section-title">图文介绍</h4>
        <div class="introduct-pics">
          <div
            v-for="(pic, index) in parseAlbumPics(currentProduct.introductPics)"
            class="introduct-pic-wrapper"
            :key="index"
          >
            <el-image
              :key="index"
              :src="pic"
              fit="contain"
              :infinite="false"
              hide-on-click-modal
              preview-teleported
              :z-index="9999"
              class="introduct-pic"
              :preview-src-list="parseAlbumPics(currentProduct.introductPics)"
              :initial-index="index"
            />
          </div>
        </div>
      </div>

      <!-- PDF文档部分 -->
      <div class="detail-section" v-if="currentProduct.pdfDocument">
        <h4 class="section-title">商品介绍文档</h4>
        <div class="pdf-document">
          <el-button type="primary" v-for="(item,index) in pdfSource" :key="index" @click="viewDocument(index)" icon="Document">
            {{`查看第${index+1}个文档`}}
          </el-button>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <template v-if="currentProduct && currentProduct.status === 0">
            <el-button type="success" @click="handleDetailApprove">
              <el-icon><Check /></el-icon> 审核通过
            </el-button>
            <el-button
              type="warning"
              @click="handleSupplement"
              style="--el-button-hover-bg-color: #e6a23c; --el-button-active-bg-color: #d4880f"
            >
              <el-icon><Refresh /></el-icon> 补充审核
            </el-button>
            <el-button type="danger" @click="handleDetailReject">
              <el-icon><Close /></el-icon> 审核拒绝
            </el-button>
          </template>
        </span>
      </template>
    </el-dialog>
    <!-- 补充审核对话框 -->
    <el-dialog
      :before-close="cancelReview"
      v-model="reviewDialogVisible"
      title="补充审核"
      width="40%"
      class="supplement-dialog"
      :close-on-click-modal="false"
    >
      <!-- 补充审核表单 -->
      <el-form>
        <!-- 补充原因输入 -->
        <el-form-item label="补充原因：" required>
          <el-select
            v-model="formData.reviewReason"
            placeholder="请选择原因类型"
            style="width: 100%"
          >
            <el-option
              v-for="item in reasonOptions"
              :key="item.label"
              :label="item.label"
              :value="item.label"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 详细说明 -->
        <el-form-item label="详细说明：">
          <el-input
            v-model="formData.detailedReasons"
            type="textarea"
            :rows="5"
            placeholder="请具体描述需要补充的材料和要求"
            show-word-limit
            maxlength="200"
          >
          </el-input>
        </el-form-item>
      </el-form>

      <!-- 对话框底部 -->
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelReview">取消</el-button>
          <el-button type="warning" @click="handleSubmitSupplement"> 发送补充通知 </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 商品介绍文档预览对话框 -->
    <el-dialog
      v-model="showPdfModal"
      title="商品详细介绍文档"
      width="82%"
      top="8vh"
      custom-class="pdf-preview-dialog"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <div class="pdf-dialog-container">
        <!-- PDF预览区域 -->
        <div class="pdf-viewport">
          <vue-pdf-embed
            :source="currentPDF"
            :page="pdfPage"
            :scale="scale"
            @loaded="handleDocumentLoad"
          />
        </div>

        <!-- 控制工具栏 -->
        <div class="pdf-controls">
          <div class="page-navigation">
            <el-button size="small" :disabled="pdfPage <= 1" @click="pdfPage--" class="mr-2">
              上一页
            </el-button>

            <span class="page-info"> 第 {{ pdfPage }} 页 / 共 {{ pageCount }} 页 </span>

            <el-button
              size="small"
              :disabled="pdfPage >= pageCount"
              @click="pdfPage++"
              class="ml-2"
            >
              下一页
            </el-button>
          </div>

          <div class="toolbar-right">
            <el-button
              size="small"
              type="primary"
              @click="downloadPdf(currentPDF)"
              class="download-btn"
            >
              下载
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch,onErrorCaptured } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Check,
  Close,
  View,
  ArrowLeft,
  ArrowRight,
  Download,
} from '@element-plus/icons-vue'
import {
  getAllProductsAdmin,
  approveProduct,
  rejectProduct,
  getProductById,
} from '../../api/product'
import { requestSupplement, type SupplementNotificationRequest } from '../../api/product'
import { getSellerById } from '@/api/seller'
import { useUserStore } from '@/stores/user'
import VuePdfEmbed from 'vue-pdf-embed'

// 路由
const router = useRouter()
const pdfSource = ref([])
const pageCount = ref(0)
const scale = ref(1)
// 状态
const loading = ref(false)
const productList = ref<any[]>([])
const searchKeyword = ref('')
const statusFilter = ref<number | null>(null)
const currentPage = ref(1)
const pageSize = ref(10)
const totalItems = ref(0)
const multipleSelection = ref<any[]>([])
const pdfPage = ref(1)
const currentPDF=ref('')
// 对话框
const detailDialogVisible = ref(false)
const currentProduct = ref<any>(null)
const showPdfModal = ref(false)

// 补充审核对话框
const reviewDialogVisible = ref(false)
// 预设补充审核原因
const reasonOptions = ref([
  { label: '材料缺失' },
  { label: '图片不清晰' },
  { label: '信息不完整' },
  { label: '其他原因' },
])
const formData = ref({
  reviewReason: '',
  detailedReasons: '',
})
// const rules = {
//   reviewReason: [
//     { required: true, message: '请选择补充审核原因', trigger: 'blur' }
//   ],
//   detailedReasons: [
//     { required: true, message: '请输入详细修改要求', trigger: 'blur' }
//   ],
// }
// const reviewReason=ref('')
// 详细说明
// const detailedExplanation=ref('')
const emailSubject = ref('商品修改要求通知')
const selectProduct = ref()
// 计算属性
const filteredProducts = computed(() => {
  let result = productList.value

  // 关键词搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(
      (item) =>
        item.name?.toLowerCase().includes(keyword) ||
        item.brandName?.toLowerCase().includes(keyword) ||
        item.productCategoryName?.toLowerCase().includes(keyword),
    )
  }

  // 状态过滤
  if (statusFilter.value !== null) {
    result = result.filter((item) => item.status === statusFilter.value)
  }

  // 更新总数
  totalItems.value = result.length

  // 分页
  const startIndex = (currentPage.value - 1) * pageSize.value
  return result.slice(startIndex, startIndex + pageSize.value)
})

// 是否有待审核商品
const hasPendingProducts = computed(() => {
  return multipleSelection.value.some((product) => product.status === 0)
})

// 初始化
onMounted(() => {
  fetchProductList()
})

// 监听搜索和过滤器变化
watch([searchKeyword, statusFilter], () => {
  currentPage.value = 1 // 重置到第一页
})
const handleDocumentLoad = (doc: any) => {
  pageCount.value = doc.numPages
}
// 获取商品列表
const fetchProductList = async () => {
  loading.value = true
  try {
    const response = await getAllProductsAdmin()
    console.log('获取商品列表成功:', response)

    if (response.code === 1 && response.data) {
      // 处理返回的数据
      productList.value = response.data.map((product: any) => {
        return {
          ...product,
          status: product.status ?? 0, // 默认为待审核
        }
      })
      totalItems.value = productList.value.length
    } else {
      ElMessage.error(response.msg || '获取商品列表失败')
    }
  } catch (error) {
    console.error('获取商品列表出错:', error)
    ElMessage.error('获取商品列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
// 下载PDF文档
const downloadPdf = (url: string) => {
  const link = document.createElement('a')
  link.href = url
  link.download = '商品介绍文档.pdf'
  link.click()
}
// 商品审核状态文本
const auditStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    0: '待审核',
    1: '已通过',
  }
  return statusMap[status] || '未知状态'
}

// 商品审核状态标签类型
const auditStatusTagType = (status: number) => {
  const typeMap: Record<number, string> = {
    0: 'warning',
    1: 'success',
    2: 'danger',
  }
  return typeMap[status] || 'info'
}

// 价格格式化
const formatPrice = (price: number) => {
  return price ? price.toFixed(2) : '0.00'
}

// 日期格式化
const formatDate = (date: string) => {
  if (!date) return '未知'
  try {
    const dateObj = new Date(date)
    return dateObj.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch (e) {
    return date
  }
}

// 解析商品属性
const parseProductAttr = (attrJson: string | null): { key: string; value: string }[] => {
  if (!attrJson) return []
  try {
    console.log('解析商品属性原始数据:', attrJson)
    const parsedData = JSON.parse(attrJson)
    console.log('解析后的商品属性:', parsedData)

    // 兼容不同格式的商品属性数据
    if (Array.isArray(parsedData)) {
      // 可能有两种格式：[{key,value}] 或 [{name,options}]
      if (parsedData.length === 0) return []

      if (parsedData[0] && 'key' in parsedData[0] && 'value' in parsedData[0]) {
        return parsedData
      } else if (parsedData[0] && 'name' in parsedData[0]) {
        // 转换 [{name,options}] 格式为 [{key,value}] 格式
        return parsedData.flatMap((attr) => {
          if (!attr.options || !Array.isArray(attr.options)) return []
          return attr.options
            .filter((opt: any) => opt && opt.name)
            .map((opt: any) => ({
              key: attr.name || '属性',
              value: opt.name || '',
            }))
        })
      }
    }

    return []
  } catch (e) {
    console.error('解析商品属性失败:', e)
    return []
  }
}

// 解析商品相册图片URL
const parseAlbumPics = (albumPicsStr: string | null): string[] => {
  if (!albumPicsStr) return []

  console.log('原始相册图片字符串:', albumPicsStr)

  // 更简单直接的方法：以http开头作为分隔标志
  const urls: string[] = []

  // 检查是否包含多个URL（以http开头）
  if (albumPicsStr.toLowerCase().indexOf('http', 1) > 0) {
    // 找到所有以http开头的位置
    const positions: number[] = []
    let pos = albumPicsStr.toLowerCase().indexOf('http')

    while (pos !== -1) {
      positions.push(pos)
      pos = albumPicsStr.toLowerCase().indexOf('http', pos + 1)
    }

    // 根据这些位置分割字符串
    for (let i = 0; i < positions.length; i++) {
      const start = positions[i]
      const end = i < positions.length - 1 ? positions[i + 1] : albumPicsStr.length
      const url = albumPicsStr.substring(start, end).trim()

      // 移除URL末尾可能的逗号
      const cleanUrl = url.endsWith(',') ? url.slice(0, -1) : url
      urls.push(cleanUrl)
    }
  } else if (albumPicsStr.toLowerCase().startsWith('http')) {
    // 单个URL的情况
    urls.push(albumPicsStr)
  }

  console.log('处理后的URLs:', urls)
  return urls
}

// 查看商品详情
const handleView = async (product: any) => {
  try {
    loading.value = true
    console.log('查看商品详情，商品ID:', product.id)

    // 使用新的API获取商品详情
    const response = await getProductById(product.id)
    console.log('获取商品详情响应:', response)

    if (response.code === 1 && response.data) {
      // 设置当前产品详情
      currentProduct.value = response.data
      if(currentProduct.value.pdfDocument){
        pdfSource.value = currentProduct.value.pdfDocument.split(',')
      }
      pdfSource.value.forEach((item)=>{
        console.log('pdf列表：',item);
      })
      
      // 确保商品相册图片格式正确
      if (currentProduct.value.albumPics) {
        console.log('商品相册原始数据:', currentProduct.value.albumPics)
        console.log('解析后的相册图片:', parseAlbumPics(currentProduct.value.albumPics))
      }
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取商品详情失败123123')
    }
  } catch (error) {
    console.error('获取商品详情失败:', error)
    ElMessage.error('获取商品详情失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 审核通过
const handleApprove = (product: any) => {
  ElMessageBox.confirm(`确定要通过商品 "${product.name}" 的审核吗？`, '审核确认', {
    confirmButtonText: '确认通过',
    cancelButtonText: '取消',
    type: 'success',
  })
    .then(async () => {
      try {
        loading.value = true
        // 使用新的API审核通过商品
        const response = await approveProduct(product.id)

        if (response.code === 1) {
          ElMessage.success('商品审核已通过')
          // 更新本地商品状态
          const foundProduct = productList.value.find((p) => p.id === product.id)
          if (foundProduct) {
            foundProduct.status = 1
          }
          // 刷新数据
          refreshData()
        } else {
          ElMessage.error(response.msg || '操作失败')
        }
      } catch (error) {
        console.error('审核通过失败:', error)
        ElMessage.error('审核通过失败，请稍后重试')
      } finally {
        loading.value = false
      }
    })
    .catch(() => {
      // 用户取消操作
    })
}
// 补充审核
const handleSupplement = async (product: any) => {
  console.log(product)
  selectProduct.value = product
  reviewDialogVisible.value = true
}
// 发送补充通知
const handleSubmitSupplement = async () => {
  // 获取当前登录的用户ID
  const userStore = useUserStore()
  const auditorsID = userStore.userInfo?.id
  if (!formData.value.reviewReason) {
    ElMessage.error('请选择补充审核的原因')
    return
  } else if (!formData.value.detailedReasons) {
    ElMessage.error('请填写补充审核的详细原因')
    return
  }
  // 获取当前商品的商家的邮箱
  const res = await getSellerById(selectProduct.value.creatorUserId)
  console.log('商家信息', selectProduct.value)

  // 收集商品补充审核信息并向商家发送信息
  const sendReviewInformation = ref<SupplementNotificationRequest>({
    subject: emailSubject.value,
    productId: selectProduct.value.id,
    merchantId: selectProduct.value.creatorUserId,
    auditorsID,
    email: res.data.email,
    reason: formData.value.reviewReason,
    detailedReasons: formData.value.detailedReasons,
    reviewTime: JSON.stringify(new Date()),
  })
  // 准备邮件格式
  // 发送邮件
  await requestSupplement(sendReviewInformation.value)
  reviewDialogVisible.value = false
}
// 取消补充审核
const cancelReview = () => {
  reviewDialogVisible.value = false
  formData.value.reviewReason = ''
  formData.value.detailedReasons = ''
}
// 拒绝审核
const handleReject = (product: any) => {
  ElMessageBox.confirm(`确定要拒绝商品 "${product.name}" 的审核并删除该商品吗？`, '审核拒绝', {
    confirmButtonText: '确认拒绝',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        loading.value = true
        // 使用新的API拒绝商品
        const response = await rejectProduct(product.id)

        if (response.code === 1) {
          ElMessage.success('已拒绝商品审核并删除该商品')
          // 从列表中移除该商品
          productList.value = productList.value.filter((p) => p.id !== product.id)
          // 更新总数
          totalItems.value = productList.value.length
          // 刷新数据展示
          refreshData()
        } else {
          ElMessage.error(response.msg || '操作失败')
        }
      } catch (error) {
        console.error('拒绝审核失败:', error)
        ElMessage.error('拒绝审核失败，请稍后重试')
      } finally {
        loading.value = false
      }
    })
    .catch(() => {
      // 用户取消操作
    })
}
const viewDocument=(index:number)=>{
  showPdfModal.value=true
  currentPDF.value=pdfSource.value[index]
}
// 搜索处理
const handleSearch = () => {
  currentPage.value = 1 // 重置到第一页
}

// 刷新数据
const refreshData = () => {
  fetchProductList()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

// 多选处理
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection
}

// 批量通过
const batchApprove = async () => {
  const pendingProducts = multipleSelection.value.filter((product) => product.status === 0)
  if (pendingProducts.length === 0) {
    ElMessage.warning('没有可批量审核的待审核商品')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要批量通过${pendingProducts.length}个商品的审核申请吗？`,
      '批量审核确认',
      {
        confirmButtonText: '确定通过',
        cancelButtonText: '取消',
        type: 'success',
      },
    )

    loading.value = true

    // 依次处理每个商品
    for (const product of pendingProducts) {
      await approveProduct(product.id)
    }

    ElMessage.success(`已批量通过${pendingProducts.length}个商品的审核`)
    refreshData()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('批量审核失败:', error)
      ElMessage.error(error.message || '批量审核操作失败')
    }
  } finally {
    loading.value = false
  }
}

// 批量拒绝
const batchReject = async () => {
  const pendingProducts = multipleSelection.value.filter((product) => product.status === 0)
  if (pendingProducts.length === 0) {
    ElMessage.warning('没有可批量拒绝的待审核商品')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要批量拒绝${pendingProducts.length}个商品的审核申请并删除这些商品吗？`,
      '批量拒绝审核',
      {
        confirmButtonText: '确定拒绝',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    loading.value = true

    // 依次处理每个商品
    for (const product of pendingProducts) {
      await rejectProduct(product.id)
    }

    ElMessage.success(`已批量拒绝${pendingProducts.length}个商品的审核并删除这些商品`)
    refreshData()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('批量拒绝失败:', error)
      ElMessage.error(error.message || '批量拒绝操作失败')
    }
  } finally {
    loading.value = false
  }
}

// 在详情对话框中审核通过
const handleDetailApprove = () => {
  if (!currentProduct.value) return

  handleApprove(currentProduct.value)
  detailDialogVisible.value = false
}

// 在详情对话框中审核拒绝
const handleDetailReject = () => {
  if (!currentProduct.value) return

  handleReject(currentProduct.value)
  detailDialogVisible.value = false
}
onErrorCaptured((err) => {
  console.error('组件错误:', err)
  return false
})
</script>

<style scoped lang="scss">
.page-container {
  padding: 20px;
}

.page-card {
  margin-bottom: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.page-title {
  font-size: 20px;
  margin: 0;
  color: #303133;
}

.page-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.search-input {
  width: 240px;
}

.page-content {
  margin-top: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.batch-actions {
  margin-bottom: 20px;
}

.price {
  color: #f56c6c;
  font-weight: 500;
}

/* 商品详情样式 */
.product-detail {
  padding: 10px;
}

.detail-header {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.product-image {
  width: 200px;
  height: 200px;
  border-radius: 8px;
  object-fit: cover;
  border: 1px solid #ebeef5;
}

.header-info {
  flex: 1;
}

.product-name {
  font-size: 20px;
  margin-top: 0;
  margin-bottom: 15px;
  color: #303133;
}

.product-meta {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.meta-item {
  display: flex;
  align-items: center;
}

.label {
  color: #909399;
  margin-right: 8px;
  min-width: 70px;
}

.value {
  color: #606266;
  font-weight: 500;
}

.section-title {
  font-size: 16px;
  border-left: 3px solid var(--el-color-primary);
  padding-left: 10px;
  margin-bottom: 15px;
}

.detail-section {
  margin-bottom: 30px;
}
.product-variant {
  margin-top: 20px;

  .variant-list {
    display: flex;
    flex-direction: column;
    gap: 12px; // 每个变体之间的间距
  }

  .variant-item {
    display: flex;
    align-items: center; // 垂直居中对齐
    gap: 12px; // 每个子元素之间的间距
    padding: 12px;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    background-color: #fff;
    flex-wrap: wrap; // 允许子元素换行（如果一行放不下）
  }

  .variant-image {
    // 固定宽度，让图片区域稳定
    display: flex;
    align-items: center;

    .variant-pic {
      border-radius: 4px;
    }

    .variant-pic-placeholder {
      width: 60px;
      height: 60px;
      border-radius: 4px;
      background-color: #f5f7fa;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #909399;
      font-size: 12px;
    }
  }

  .variant-attrs,
  .variant-size,
  .variant-sku,
  .variant-price,
  .variant-inventory {
    display: flex;
    align-items: center;
    font-size: 14px;

    strong {
      color: #303133;
      margin-right: 4px;
      min-width: 40px; // 让标签对齐
    }

    span {
      color: #606266;
      white-space: nowrap; // 防止文字换行，有助于横向排列
    }
  }

  // 响应式调整（可选）
  @media (max-width: 768px) {
    .variant-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      .variant-image {
        align-self: flex-start;
      }
    }

    .variant-attrs,
    .variant-size,
    .variant-sku,
    .variant-price,
    .variant-inventory {
      width: 100%;
      justify-content: space-between; // 可选，让标签和值两端对齐
    }
  }
}
/* 父容器：Flex 布局 */
.introduct-pics {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

/* 图片容器：固定宽高 + 占位防止 CLS */
.introduct-pic-wrapper {
  width: 100%;
  max-width: 300px; /* 限制最大宽度 */
  height: 300px; /* 固定高度 */
  flex-shrink: 0; /* 防止压缩 */
}

/* 图片本身：填充容器 + 悬停动画 */
.introduct-pic {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  cursor: pointer;
  transition: transform 0.3s;
  transform: translateZ(0); /* GPU 加速 */
}

.introduct-pic:hover {
  transform: scale(1.02) translateZ(0);
}

/* CLS 优化：预加载占位 */
.introduct-pic-wrapper:empty::before {
  content: '';
  display: block;
  padding-top: 100%; /* 根据实际宽高比调整 */
}

/* PDF文档样式 */
.pdf-document {
  display: flex;
  gap: 15px;
  align-items: center;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .introduct-pic {
    width: 100%;
    height: auto;
  }

  .pdf-document {
    flex-direction: column;
    align-items: flex-start;
  }
}
.attribute-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
}

.attribute-item {
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.attr-name {
  color: #909399;
  margin-right: 5px;
}

.attr-value {
  color: #606266;
  font-weight: 500;
}

/* 父容器：Flex 布局 */
.album-pics {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

/* 图片容器：固定尺寸 + 悬停动画 */
.album-pic-wrapper {
  width: 120px;
  height: 120px;
  flex-shrink: 0; /* 防止压缩 */
}

/* 图片本身：填充 + 交互 */
.album-pic {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  cursor: pointer;
  transition: transform 0.3s;
  transform: translateZ(0); /* GPU加速 */
}

.album-pic:hover {
  transform: scale(1.05);
}

.product-description {
  padding: 15px;
  background-color: #f9fafc;
  border-radius: 8px;
  min-height: 100px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .page-actions {
    width: 100%;
  }

  .search-input {
    width: 100%;
  }

  .detail-header {
    flex-direction: column;
  }

  .product-image {
    width: 100%;
    height: auto;
    max-height: 300px;
  }

  .product-meta {
    grid-template-columns: 1fr;
  }

  .attribute-list {
    grid-template-columns: 1fr;
  }
}

.pdf-preview-dialog {
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.pdf-dialog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.pdf-viewport {
  flex: 1;
  overflow-y: auto;
  max-height: 65vh;
  border: 1px solid #eee;
  margin-bottom: 16px;
}

.pdf-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-top: 1px solid #eee;
}

.page-navigation {
  display: flex;
  align-items: center;
}

.page-info {
  margin: 0 12px;
  font-size: 14px;
  color: #666;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.download-btn {
  margin-left: 8px;
}
</style>
