{"name": "vue3_shop.sharewharf.latest", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --port 88", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueup/vue-quill": "^1.2.0", "axios": "^1.8.3", "echarts": "^5.6.0", "element-plus": "^2.9.6", "i": "^0.3.7", "lodash": "^4.17.21", "lucide-vue-next": "^0.482.0", "pdfjs-dist": "^5.3.93", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-pdf-embed": "^2.1.3", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/node": "^22.13.9", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "sass": "^1.86.0", "typescript": "~5.8.0", "vite": "^6.2.1", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}