<template>
  <div class="main-container" :class="{ 'sidebar-collapsed': isSidebarCollapsed }">
    <!-- 左侧侧边栏 -->
    <div class="sidebar" :style="{ width: sidebarWidth + 'px' }" ref="sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <img src="../assets/logo.svg" alt="Logo" class="logo-img" />
          <transition name="fade">
            <h1 v-if="!isSidebarCollapsed">商城管理系统</h1>
          </transition>
        </div>
        <el-icon class="collapse-icon" @click="toggleSidebar">
          <Fold v-if="!isSidebarCollapsed" />
          <Expand v-else />
        </el-icon>
      </div>

      <!-- 用户信息区域 -->
      <transition name="fade" mode="out-in">
        <div class="user-profile" v-if="!isSidebarCollapsed" key="expanded">
          <el-avatar
            :size="50"
            :src="
              userInfo?.avatar ||
              sellerDetail?.photoUrl ||
              'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
            "
          />
          <div class="user-info">
            <h3>{{ sellerDetail?.shopName || userInfo?.accountName || '未登录' }}</h3>
            <p>{{ sellerDetail?.email || userInfo?.role || '商家账户' }}</p>
          </div>
        </div>
        <div class="user-profile-collapsed" v-else key="collapsed">
          <el-avatar
            :size="40"
            :src="
              userInfo?.avatar ||
              sellerDetail?.photoUrl ||
              'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
            "
          />
        </div>
      </transition>

      <!-- 导航菜单 -->
      <el-menu
        ref="sideMenu"
        :default-active="activeMenu"
        class="sidebar-menu"
        :collapse="isSidebarCollapsed"
        @select="handleMenuSelect"
        :unique-opened="true"
        :collapse-transition="true"
        :default-openeds="openedMenus"
        @open="handleSubMenuOpen"
        @close="handleSubMenuClose"
      >
        <el-menu-item index="welcome">
          <el-icon><House /></el-icon>
          <template #title>欢迎页面</template>
        </el-menu-item>

        <el-menu-item v-if="hasPermission('dashboard')" index="dashboard">
          <el-icon><Odometer /></el-icon>
          <template #title>控制台</template>
        </el-menu-item>

        <el-sub-menu
          v-if="
            hasPermission('Product') ||
            hasPermission('ProductList') ||
            hasPermission('ProductAdd') ||
            hasPermission('ProductEdit') ||
            hasPermission('ProductAudit')
          "
          index="products"
        >
          <template #title>
            <el-icon><Goods /></el-icon>
            <span>商品管理</span>
          </template>
          <el-menu-item v-if="hasPermission('ProductList')" index="product-list"
            >商品列表</el-menu-item
          >
          <el-menu-item v-if="hasPermission('ProductAdd')" index="product-add"
            >添加商品</el-menu-item
          >
        </el-sub-menu>

        <el-sub-menu
          v-if="hasPermission('Category') || hasPermission('CategoryList')"
          index="categories"
        >
          <template #title>
            <el-icon><Files /></el-icon>
            <span>分类管理</span>
          </template>
          <el-menu-item v-if="hasPermission('CategoryList')" index="category-list"
            >分类列表</el-menu-item
          >
        </el-sub-menu>

        <el-sub-menu index="user-list">
          <template #title>
            <el-icon><User /></el-icon>
            <span>用户管理</span>
          </template>
          <el-menu-item index="user-list"
            >用户列表</el-menu-item
          >
        </el-sub-menu>

        <el-sub-menu v-if="hasPermission('Seller') || hasPermission('SellerAudit')" index="sellers">
          <template #title>
            <el-icon><UserFilled /></el-icon>
            <span>商家管理</span>
          </template>
          <el-menu-item v-if="hasPermission('SellerAudit')" index="seller-audit"
            >商家审核</el-menu-item
          >
        </el-sub-menu>

        <el-sub-menu
          v-if="
            hasPermission('Permission') ||
            hasPermission('SellerPermission') ||
            hasPermission('RuleManagement')
          "
          index="permission"
        >
          <template #title>
            <el-icon><Lock /></el-icon>
            <span>权限管理</span>
          </template>
          <el-menu-item v-if="hasPermission('SellerPermission')" index="seller-permission"
            >商家权限</el-menu-item
          >
          <el-menu-item v-if="hasPermission('RuleManagement')" index="rule-management"
            >权限规则</el-menu-item
          >
        </el-sub-menu>

        <el-menu-item v-if="hasPermission('ProductAudit')" index="product-audit">
          <el-icon><Document /></el-icon>
          <template #title>商品审核</template>
        </el-menu-item>

        <!-- 订单管理 -->
        <el-sub-menu
          v-if="hasPermission('Order') || hasPermission('OrderList') || hasPermission('OrderManagement')"
          index="orders"
        >
          <template #title>
            <el-icon><ShoppingCart /></el-icon>
            <span>订单管理</span>
          </template>
          <el-menu-item
            v-if="hasPermission('OrderList')"
            index="order-list"
          >
            订单列表
          </el-menu-item>
          <el-menu-item
            v-if="hasPermission('OrderManagement')"
            index="order-processing"
          >
            处理中订单
          </el-menu-item>
          <el-menu-item
            v-if="hasPermission('OrderManagement')"
            index="order-completed"
          >
            已完成订单
          </el-menu-item>
        </el-sub-menu>

        <!-- 订单统计 -->
        <el-menu-item v-if="hasPermission('OrderStatistics')" index="order-statistics">
          <el-icon><DataAnalysis /></el-icon>
          <template #title>订单统计</template>
        </el-menu-item>

        <!-- 退款管理 -->
        <el-sub-menu
          v-if="hasPermission('RefundManagement') || hasPermission('RefundList') || hasPermission('RefundApproval')"
          index="refund"
        >
          <template #title>
            <el-icon><Money /></el-icon>
            <span>退款管理</span>
          </template>
          <el-menu-item
            v-if="hasPermission('RefundList')"
            index="refund-list"
          >
            退款列表
          </el-menu-item>
          <el-menu-item
            v-if="hasPermission('RefundApproval')"
            index="refund-pending"
          >
            待审核退款
          </el-menu-item>
        </el-sub-menu>

        <!-- 物流管理 -->
        <el-sub-menu
          v-if="hasPermission('TrackingManagement') || hasPermission('TrackingList') || hasPermission('TrackingRegister')"
          index="tracking"
        >
          <template #title>
            <el-icon><Van /></el-icon>
            <span>物流管理</span>
          </template>
          <el-menu-item
            v-if="hasPermission('TrackingList')"
            index="tracking-list"
          >
            物流列表
          </el-menu-item>
          <el-menu-item
            v-if="hasPermission('TrackingRegister')"
            index="tracking-register"
          >
            注册物流
          </el-menu-item>
        </el-sub-menu>

        <!-- 回款信息/回款管理 -->
        <el-menu-item
          v-if="hasPermission('SettlementInfo')"
          index="settlement-merchant"
          @click="router.push('/main/settlement/merchant')"
        >
          <el-icon><Money /></el-icon>

          <span>回款信息</span>
        </el-menu-item>

        <el-menu-item
          v-if="hasPermission('SettlementManagement')"
          index="settlement-admin"
          @click="router.push('/main/settlement/admin')"
        >
          <el-icon><Money /></el-icon>
          <span>回款管理</span>
        </el-menu-item>

        <!-- 站内信 -->
        <el-sub-menu
          v-if="hasPermission('Message') || hasPermission('MessageList') || hasPermission('MessageSend')"
          index="message"
        >
          <template #title>
            
            <el-icon><ChatDotRound /></el-icon>
            <span>站内信管理</span>


        </template>
        <el-menu-item 
          v-if="hasPermission('MessageList')" 
          index="message-list"
          @click="router.push('/main/message/list')"
        >
          消息列表
        </el-menu-item>
        </el-sub-menu>

        <!-- 商家子账号 -->
        <el-sub-menu
          v-if="(currentUser.role !== '超级管理员' && currentUser.role !== '管理员') &&
    (hasPermission('StaffAccount') || hasPermission('ManageStaff'))"
          index="staff-accounts"
        >
          <template #title>
            <el-icon><Avatar /></el-icon>
            <span>子账号管理</span>
          </template>
          <el-menu-item
            v-if="hasPermission('ManageStaff')"
            index="staff-account"
            @click="
              () => {
                console.log('直接点击店铺子账号')
                router.push('/main/staff-accounts/staff-account')
              }
            "
          >
            店铺子账号
          </el-menu-item>
        </el-sub-menu>
        <!-- 平台管理员账号 -->
        <el-sub-menu v-if="hasPermission('AdminAccount')" index="admin-accounts">
          <template #title>
            <el-icon><UserFilled /></el-icon>
            <span>平台管理员账号</span>
          </template>
          <el-menu-item
            v-if="hasPermission('AdminAccount')"
            index="admin-account-list"
            @click="
              () => {
                console.log('直接点击管理员账号管理')
                router.push('/main/admin-accounts/list')
              }
            "
          >
            管理员账号管理
          </el-menu-item>
        </el-sub-menu>

        <el-sub-menu
          v-if="
            hasPermission('Commission') ||
            hasPermission('LeaderList') ||
            hasPermission('AddLeader') ||
            hasPermission('CommissionSettings') ||
            hasPermission('InvitationCodes') ||
            hasPermission('CommissionStatistics')
          "
          index="commission"
        >
          <template #title>
            <el-icon><Money /></el-icon>
            <span>佣金模式</span>
          </template>
          <el-menu-item
            v-if="hasPermission('LeaderList')"
            index="leader-list"
            @click="
              () => {
                console.log('直接点击团长管理')
                router.push('/main/commission/leader-list')
              }
            "
          >
            团长管理
          </el-menu-item>
          <el-menu-item
            v-if="hasPermission('AddLeader')"
            index="add-leader"
            @click="
              () => {
                console.log('直接点击添加团长')
                router.push('/main/commission/add-leader')
              }
            "
          >
            添加团长
          </el-menu-item>
          <el-menu-item
            v-if="hasPermission('CommissionSettings')"
            index="commission-settings"
            @click="
              () => {
                console.log('直接点击佣金设置')
                router.push('/main/commission/commission-settings')
              }
            "
          >
            佣金设置
          </el-menu-item>
          <el-menu-item
            v-if="hasPermission('InvitationCodes')"
            index="invitation-codes"
            @click="
              () => {
                console.log('直接点击邀请码管理')
                router.push('/main/commission/invitation-codes')
              }
            "
          >
            邀请码管理
          </el-menu-item>
          <el-menu-item
            v-if="hasPermission('CommissionStatistics')"
            index="commission-statistics"
            @click="
              () => {
                console.log('直接点击佣金统计')
                router.push('/main/commission/commission-statistics')
              }
            "
          >
            佣金统计
          </el-menu-item>
        </el-sub-menu>


        <el-sub-menu v-if="hasPermission('help') || hasPermission('help-docs')" index="help">
          <template #title>
            <el-icon><QuestionFilled /></el-icon>
            <span>帮助中心</span>
          </template>
          <el-menu-item v-if="hasPermission('help-docs')" index="help-docs">帮助文档</el-menu-item>
        </el-sub-menu>
      </el-menu>

      <!-- 侧边栏底部工具区 -->
      <transition name="fade" mode="out-in">
        <div class="sidebar-footer" v-if="!isSidebarCollapsed" key="footer-expanded">
          <el-button class="help-button" text @click="showHelpDialog">
            <el-icon><QuestionFilled /></el-icon>
            <span>帮助中心</span>
          </el-button>
          <el-button class="logout-button" type="danger" text @click="handleLogout">
            <el-icon><SwitchButton /></el-icon>
            <span>退出登录</span>
          </el-button>
        </div>
        <div class="sidebar-footer-collapsed" v-else key="footer-collapsed">
          <el-button class="icon-button" text @click="showHelpDialog">
            <el-icon><QuestionFilled /></el-icon>
          </el-button>
          <el-button class="icon-button" type="danger" text @click="handleLogout">
            <el-icon><SwitchButton /></el-icon>
          </el-button>
        </div>
      </transition>

      <!-- 拖动调整宽度的手柄 -->
      <div class="resize-handle" v-if="!isSidebarCollapsed" @mousedown="startResizing"></div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="main-content" :style="{ marginLeft: contentMarginLeft + 'px' }">
      <!-- 顶部导航栏 -->
      <div class="top-navbar">
        <div class="navbar-left">
          <div class="menu-toggle" @click="toggleSidebar">
            <el-icon><Fold v-if="isSidebarCollapsed" /> <Expand v-else /></el-icon>
          </div>
          <div class="breadcrumb-container" v-if="breadcrumbItems.length">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item v-for="(item, index) in breadcrumbItems" :key="index">
                {{ item }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="merchant-name" v-if="sellerDetail?.shopName">
            <span>{{ sellerDetail.shopName }}</span>
          </div>
        </div>

        <div class="navbar-right">
          <!-- 语言切换按钮 -->
          <el-dropdown trigger="click" @command="handleLanguageChange" class="language-dropdown">
            <div class="nav-icon-btn">
              <el-icon><Message /></el-icon>
              <span class="language-text">{{ currentLanguage }}</span>
              <el-icon class="arrow-icon"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="english">
                  <div class="language-item">
                    <span :class="{ 'active-language': currentLanguage === 'English' }"
                      >English</span
                    >
                  </div>
                </el-dropdown-item>
                <el-dropdown-item command="chinese_simplified">
                  <div class="language-item">
                    <span :class="{ 'active-language': currentLanguage === '简体中文' }"
                      >简体中文</span
                    >
                  </div>
                </el-dropdown-item>
                <el-dropdown-item command="chinese_traditional">
                  <div class="language-item">
                    <span :class="{ 'active-language': currentLanguage === '繁體中文' }"
                      >繁體中文</span
                    >
                  </div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <!-- 帮助中心按钮 -->
          <el-tooltip content="帮助中心" placement="bottom" effect="light">
            <div class="nav-icon-btn" @click="goToHelpDocs">
              <el-icon><Document /></el-icon>
            </div>
          </el-tooltip>

          <!-- 用户信息 -->
          <el-dropdown trigger="click" @command="handleCommand">
            <div class="user-dropdown-link">
              <el-avatar :size="32" :src="userStore.userInfo?.avatar || ''" class="user-avatar">
                {{ userStore.userInfo?.accountName?.charAt(0)?.toUpperCase() || 'U' }}
              </el-avatar>
              <span class="username">{{ userStore.userInfo?.accountName || '未登录' }}</span>
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon> 个人信息
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon> 账户设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><Switch /></el-icon> 退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 页面内容区域 -->
      <div class="page-content">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </div>
    </div>

    <!-- 添加帮助中心对话框 -->
    <el-dialog
      v-model="helpDialogVisible"
      title="帮助中心"
      width="500px"
      class="help-dialog"
      :close-on-click-modal="true"
      :before-close="closeHelpDialog"
    >
      <div class="help-content">
        <div class="help-section">
          <h3>快速入门</h3>
          <ul>
            <li>
              <el-icon><InfoFilled /></el-icon>
              <span>导航栏：左侧边栏提供主要功能导航</span>
            </li>
            <li>
              <el-icon><InfoFilled /></el-icon>
              <span>自定义视图：可通过拖动边栏右侧调整宽度</span>
            </li>
            <li>
              <el-icon><InfoFilled /></el-icon>
              <span>折叠菜单：点击顶部折叠按钮可收起菜单</span>
            </li>
          </ul>
        </div>

        <div class="help-section">
          <h3>常见功能</h3>
          <ul>
            <li>
              <el-icon><Document /></el-icon>
              <span>商品管理：添加、编辑和管理您的商品</span>
            </li>
            <li>
              <el-icon><ShoppingCart /></el-icon>
              <span>订单处理：查看和处理客户订单</span>
            </li>
            <li>
              <el-icon><Promotion /></el-icon>
              <span>营销管理：创建优惠券和促销活动</span>
            </li>
          </ul>
        </div>

        <div class="help-section">
          <h3>联系支持</h3>
          <p>
            <el-icon><Message /></el-icon>
            <span>技术支持: <EMAIL></span>
          </p>
          <p>
            <el-icon><Phone /></el-icon>
            <span>客服热线: ************</span>
          </p>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeHelpDialog">关闭</el-button>
          <el-button type="primary" @click="goToHelpDocs">查看更多帮助文档</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { getSellerById, type SellerDetailInfo, logout } from '@/api/seller'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import {
  Timer,
  Delete,
  Bell,
  Document,
  House,
  Odometer,
  Goods,
  Files,
  ShoppingCart,
  User,
  Promotion,
  Setting,
  SwitchButton,
  QuestionFilled,
  Fold,
  Expand,
  ArrowDown,
  InfoFilled,
  Message,
  UserFilled,
  Money,
  ChatDotRound,
  Lock,
  Avatar,
  DataAnalysis,
  Van,
} from '@element-plus/icons-vue'
// 导入语言服务
import { changeLanguage, getCurrentLanguage, getLanguageDisplayName } from '../utils/language'
import type { LanguageType } from '../utils/language'
import { getUnreadCount } from '@/api/message'

// 路由和状态管理
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const currentUser=JSON.parse(localStorage.getItem('user_info') || '{}')

// 检查用户是否有指定权限
const hasPermission = (permission: string): boolean => {
  // 临时允许所有用户访问子账号管理功能
  // if (permission === 'ManageStaff' || permission === 'StaffAccount' || permission==='AdminAccount') {
  //   return true
  // }
  return userStore.hasPermission(permission)
}

// 导航历史记录
const navHistory = ref<string[]>([])

// 顶部导航相关变量
const breadcrumbItems = ref<string[]>([])
const hasUnreadNotifications = ref(false)

// 用户信息的计算属性
const userInfo = computed(() => userStore.userInfo)

// 侧边栏状态
const isSidebarCollapsed = ref(false)
const sidebarWidth = ref(240) // 默认宽度
const minSidebarWidth = 240 // 最小宽度
const maxSidebarWidth = 400 // 最大宽度
const sidebar = ref<HTMLElement | null>(null)
const sideMenu = ref<any>(null) // 添加sideMenu引用

// 保存展开的菜单
const openedMenus = ref<string[]>([])

// 监听菜单展开/折叠
const handleSubMenuOpen = (index: string) => {
  if (!openedMenus.value.includes(index)) {
    openedMenus.value.push(index)
    // 保存到本地存储
    localStorage.setItem('openedMenus', JSON.stringify(openedMenus.value))
  }
}

// 监听菜单关闭
const handleSubMenuClose = (index: string) => {
  const idx = openedMenus.value.indexOf(index)
  if (idx > -1) {
    openedMenus.value.splice(idx, 1)
    // 保存到本地存储
    localStorage.setItem('openedMenus', JSON.stringify(openedMenus.value))
  }
}

// 计算主内容的左边距
const contentMarginLeft = computed(() => {
  return isSidebarCollapsed.value ? 64 : sidebarWidth.value
})

// 调整侧边栏宽度相关
const isResizing = ref(false)
const startX = ref(0)
const startWidth = ref(0)

// 开始调整大小
const startResizing = (e: MouseEvent) => {
  isResizing.value = true
  startX.value = e.clientX
  startWidth.value = sidebarWidth.value

  document.addEventListener('mousemove', resize)
  document.addEventListener('mouseup', stopResizing)
  document.body.style.cursor = 'ew-resize'
  document.body.style.userSelect = 'none' // 防止拖动时选中文本
}

// 调整大小
const resize = (e: MouseEvent) => {
  if (!isResizing.value) return

  const deltaX = e.clientX - startX.value
  let newWidth = startWidth.value + deltaX

  // 限制宽度范围
  if (newWidth < minSidebarWidth) newWidth = minSidebarWidth
  if (newWidth > maxSidebarWidth) newWidth = maxSidebarWidth

  sidebarWidth.value = newWidth
  localStorage.setItem('sidebarWidth', String(newWidth))
}

// 停止调整大小
const stopResizing = () => {
  isResizing.value = false
  document.removeEventListener('mousemove', resize)
  document.removeEventListener('mouseup', stopResizing)
  document.body.style.cursor = ''
  document.body.style.userSelect = ''
}

const toggleSidebar = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value
  // 保存到本地存储
  localStorage.setItem('sidebarCollapsed', String(isSidebarCollapsed.value))

  // 触发窗口resize事件，使界面元素自适应
  setTimeout(() => {
    window.dispatchEvent(new Event('resize'))
  }, 300)
}

// 活跃菜单
const activeMenu = ref('welcome')
const breadcrumbs = ref(['欢迎页面'])

// 从路径获取页面名称
const getPageNameFromPath = (path: string) => {
  if (path.includes('/dashboard')) return '控制台'
  if (path.includes('/user-management')) {
    if (path.includes('/user-list')) return '用户管理 / 用户列表'
    if (path.includes('/purchase-analysis')) return '用户管理 / 购买分析'
    if (path.includes('/user-detail')) return '用户管理 / 用户详情'
    return '用户管理'
  }
  if (path.includes('/message')) {
    if (path.includes('/message-list')) return '站内信 / 消息管理'
    if (path.includes('/message-detail')) return '站内信 / 消息详情'
    if (path.includes('/message-send')) return '站内信 / 发送消息'
    return '站内信'
  }
  if (path.includes('/permission')) {
    if (path.includes('/seller-permission')) return '权限管理 / 商家权限'
    if (path.includes('/rule-management')) return '权限管理 / 权限规则'
    return '权限管理'
  }
  if (path.includes('/product')) {
    if (path.includes('/category')) return '商品管理 / 分类管理'
    if (path.includes('/list')) return '商品管理 / 商品列表'
    if (path.includes('/add')) return '商品管理 / 添加商品'
    if (path.includes('/audit')) return '商品管理 / 商品审核'
    return '商品管理'
  }
  if (path.includes('/staff-accounts')) {
    if (path.includes('/staff-account')) return '子账号管理 / 店铺子账号'
    return '子账号管理'
  }
  if (path.includes('/commission')) {
    if (path.includes('/leader-list')) return '佣金模式 / 团长管理'
    if (path.includes('/add-leader')) return '佣金模式 / 添加团长'
    if (path.includes('/edit-leader')) return '佣金模式 / 编辑团长'
    if (path.includes('/commission-settings')) return '佣金模式 / 佣金设置'
    if (path.includes('/invitation-codes')) return '佣金模式 / 邀请码管理'
    if (path.includes('/commission-statistics')) return '佣金模式 / 佣金统计'
    return '佣金模式'
  }
  if (path.includes('/settings')) return '系统设置'
  if (path.includes('/welcome')) return '欢迎页面'
  return path.split('/').pop() || '未知页面'
}

// 从面包屑获取路径
const getPathFromBreadcrumb = (items: string[]) => {
  if (items.length === 0) return '/main/welcome'

  // 根据面包屑文本确定路径
  if (items.length === 1) {
    switch (items[0]) {
      case '欢迎页面':
        return '/main/welcome'
      case '控制台':
        return '/main/dashboard'
      case '系统设置':
        return '/main/settings'
      default:
        return '/main/welcome'
    }
  } else if (items.length === 2) {
    const [category, page] = items

    if (category === '商品管理') {
      switch (page) {
        case '商品列表':
          return '/main/product/list'
        case '添加商品':
          return '/main/product/add'
        case '商品审核':
          return '/main/product/audit'
        default:
          return '/main/product/list'
      }
    } else if (category === '分类管理') {
      switch (page) {
        case '分类列表':
          return '/main/category/list'
        default:
          return '/main/category/list'
      }
    } else if (category === '用户管理') {
      switch (page) {
        case '用户列表':
          return '/main/user-management/user-list'
        case '购买分析':
          return '/main/user-management/purchase-analysis'
        default:
          return '/main/user-management/user-list'
      }
    } else if (category === '佣金模式') {
      console.log('从面包屑导航到佣金模式页面:', page)
      switch (page) {
        case '团长管理':
          return '/main/commission/leader-list'
        case '添加团长':
          return '/main/commission/add-leader'
        case '佣金设置':
          return '/main/commission/commission-settings'
        case '邀请码管理':
          return '/main/commission/invitation-codes'
        case '佣金统计':
          return '/main/commission/commission-statistics'
        default:
          return '/main/commission/leader-list'
      }
    } else if (category === '站内信') {
      switch (page) {
        case '消息管理':
          return '/main/message/list'
        default:
          return '/main/message/list'
      }
    } else if (category === '权限管理') {
      switch (page) {
        case '商家权限':
          return '/main/permission/seller'
        case '权限规则':
          return '/main/permission/rules'
        default:
          return '/main/permission/seller'
      }
    } else if (category === '订单管理') {
      switch (page) {
        case '订单列表':
          return '/main/order/list'
        case '处理中订单':
          return '/main/order/processing'
        case '已完成订单':
          return '/main/order/completed'
        case '退款处理':
          return '/main/order/refund'
        default:
          return '/main/order/list'
      }
    } else if (category === '客户管理') {
      switch (page) {
        case '客户列表':
          return '/main/customer/list'
        case '客户反馈':
          return '/main/customer/feedback'
        default:
          return '/main/customer/list'
      }
    } else if (category === '营销管理') {
      switch (page) {
        case '优惠券':
          return '/main/marketing/coupons'
        case '折扣活动':
          return '/main/marketing/discounts'
        default:
          return '/main/marketing/coupons'
      }
    } else if (category === '用户') {
      switch (page) {
        case '个人信息':
          return '/main/user/profile'
        case '账户设置':
          return '/main/user/settings'
        default:
          return '/main/user/profile'
      }
    }
  } else if (
    items.length === 3 &&
    items[0] === '佣金模式' &&
    items[1] === '团长管理' &&
    items[2] === '编辑团长'
  ) {
    // 处理编辑团长的情况，需要从当前路由获取团长ID
    const id = route.params.id
    return `/main/commission/edit-leader/${id}`
  } else if (
    items.length === 3 &&
    items[0] === '用户管理' &&
    items[1] === '用户列表' &&
    items[2] === '用户详情'
  ) {
    // 处理用户详情的情况，需要从当前路由获取用户ID
    const id = route.params.id
    return `/main/user-management/user-detail/${id}`
  }

  return '/main/welcome'
}

// 导航到面包屑
const navigateToBreadcrumb = (items: string[]) => {
  const path = getPathFromBreadcrumb(items)
  if (path && path !== route.path) {
    // 添加到导航历史
    addToHistory(route.path)
    router.push(path)
  }
}

// 导航到历史记录
const navigateToHistory = (path: string) => {
  if (path && path !== route.path) {
    addToHistory(route.path)
    router.push(path)
  }
}

// 添加路径到历史记录
const addToHistory = (path: string) => {
  // 不添加相同的连续路径
  if (navHistory.value.length > 0 && navHistory.value[navHistory.value.length - 1] === path) {
    return
  }

  navHistory.value.push(path)

  // 最多保存15条记录
  if (navHistory.value.length > 15) {
    navHistory.value.shift()
  }

  // 保存历史记录到本地存储
  localStorage.setItem('navHistory', JSON.stringify(navHistory.value))
}

// 清除历史记录
const clearHistory = () => {
  navHistory.value = []
  localStorage.removeItem('navHistory')
  ElMessage.success('历史记录已清除')
}



// 处理菜单选择
const handleMenuSelect = (index: string) => {
  console.log('菜单点击:', index)
  console.log('当前用户权限:', userStore.permissions)

  activeMenu.value = index

  // 更新面包屑和目标路径
  let targetPath = ''
  let requiredPermission = ''

  switch (index) {
    case 'welcome':
      breadcrumbs.value = ['欢迎页面']
      targetPath = '/main/welcome'
      break
    case 'dashboard':
      breadcrumbs.value = ['控制台']
      targetPath = '/main/dashboard'
      requiredPermission = 'dashboard'
      break
    case 'product-list':
      breadcrumbs.value = ['商品管理', '商品列表']
      targetPath = '/main/product/list'
      requiredPermission = 'ProductList'
      break
    case 'product-add':
      breadcrumbs.value = ['商品管理', '添加商品']
      targetPath = '/main/product/add'
      break
    case 'product-audit':
      breadcrumbs.value = ['商品审核']
      targetPath = '/main/product/audit'
      break
    case 'category-list':
      breadcrumbs.value = ['分类管理', '分类列表']
      targetPath = '/main/category/list'
      break
    case 'seller-audit':
      breadcrumbs.value = ['商家管理', '商家审核']
      targetPath = '/main/seller/audit'
      break
    // 用户管理相关
    case 'user-list':
      breadcrumbs.value = ['用户管理', '用户列表']
      targetPath = '/main/user-management/user-list'
      break
    case 'purchase-analysis':
      breadcrumbs.value = ['用户管理', '购买分析']
      targetPath = '/main/user-management/purchase-analysis'
      break
    // 佣金模式相关
    case 'leader-list':
      console.log('点击团长管理菜单')
      breadcrumbs.value = ['佣金模式', '团长管理']
      targetPath = '/main/commission/leader-list'
      requiredPermission = 'LeaderList'
      break
    case 'add-leader':
      console.log('点击添加团长菜单')
      breadcrumbs.value = ['佣金模式', '添加团长']
      targetPath = '/main/commission/add-leader'
      requiredPermission = 'AddLeader'
      break
    case 'commission-settings':
      console.log('点击佣金设置菜单')
      breadcrumbs.value = ['佣金模式', '佣金设置']
      targetPath = '/main/commission/commission-settings'
      requiredPermission = 'CommissionSettings'
      break
    case 'invitation-codes':
      console.log('点击邀请码管理菜单')
      breadcrumbs.value = ['佣金模式', '邀请码管理']
      targetPath = '/main/commission/invitation-codes'
      requiredPermission = 'InvitationCodes'
      break
    case 'commission-statistics':
      console.log('点击佣金统计菜单')
      breadcrumbs.value = ['佣金模式', '佣金统计']
      targetPath = '/main/commission/commission-statistics'
      requiredPermission = 'CommissionStatistics'
      break
    case 'order-list':
      breadcrumbs.value = ['订单管理', '订单列表']
      targetPath = '/main/order/list'
      break
    case 'order-processing':
      breadcrumbs.value = ['订单管理', '处理中订单']
      targetPath = '/main/order/processing'
      break
    case 'order-completed':
      breadcrumbs.value = ['订单管理', '已完成订单']
      targetPath = '/main/order/completed'
      break
    case 'order-refund':
      breadcrumbs.value = ['订单管理', '退款处理']
      targetPath = '/main/order/refund'
      requiredPermission = 'OrderManagement'
      break
    case 'order-statistics':
      breadcrumbs.value = ['订单统计']
      targetPath = '/main/order/statistics'
      requiredPermission = 'OrderStatistics'
      break
    case 'refund-list':
      breadcrumbs.value = ['退款管理', '退款列表']
      targetPath = '/main/refund/list'
      requiredPermission = 'RefundList'
      break
    case 'refund-pending':
      breadcrumbs.value = ['退款管理', '待审核退款']
      targetPath = '/main/refund/pending'
      requiredPermission = 'RefundApproval'
      break
    case 'tracking-list':
      breadcrumbs.value = ['物流管理', '物流列表']
      targetPath = '/main/tracking/list'
      requiredPermission = 'TrackingList'
      break
    case 'tracking-register':
      breadcrumbs.value = ['物流管理', '注册物流']
      targetPath = '/main/tracking/register'
      requiredPermission = 'TrackingRegister'
      break
    case 'customer-list':
      breadcrumbs.value = ['客户管理', '客户列表']
      targetPath = '/main/customer/list'
      break
    case 'customer-feedback':
      breadcrumbs.value = ['客户管理', '客户反馈']
      targetPath = '/main/customer/feedback'
      break
    case 'marketing-coupons':
      breadcrumbs.value = ['营销管理', '优惠券']
      targetPath = '/main/marketing/coupons'
      break
    case 'marketing-discounts':
      breadcrumbs.value = ['营销管理', '折扣活动']
      targetPath = '/main/marketing/discounts'
      break
    case 'settings':
      breadcrumbs.value = ['系统设置']
      targetPath = '/main/settings'
      break
    case 'message-list':
      breadcrumbs.value = ['站内信', '消息管理']
      targetPath = '/main/message/list'
      break
    case 'seller-permission':
      breadcrumbs.value = ['权限管理', '商家权限']
      targetPath = '/main/permission/seller'
      requiredPermission = 'SellerPermission'
      break
    case 'rule-management':
      breadcrumbs.value = ['权限管理', '权限规则']
      targetPath = '/main/permission/rules'
      requiredPermission = 'RuleManagement'
      break
    case 'product-audit':
      breadcrumbs.value = ['商品审核']
      targetPath = '/main/product/audit'
      break
    case 'staff-account':
      breadcrumbs.value = ['子账号管理', '店铺子账号']
      targetPath = '/main/staff-accounts/staff-account'
      break
    default:
      breadcrumbs.value = ['控制台']
      targetPath = '/main/dashboard'
  }

  // 权限检查
  if (requiredPermission) {
    console.log(`检查权限: ${requiredPermission}`, hasPermission(requiredPermission))
    if (!hasPermission(requiredPermission)) {
      ElMessage.error(`您没有访问该页面的权限: ${requiredPermission}`)
      return
    }
  }

  // 如果当前路径不同，则添加到导航历史并跳转
  if (route.path !== targetPath) {
    console.log('准备跳转到:', targetPath)
    console.log('当前路径:', route.path)

    // 添加当前路径到导航历史
    addToHistory(route.path)

    try {
      console.log('开始路由跳转...')
      router
        .push(targetPath)
        .then(() => {
          console.log('路由跳转成功到:', targetPath)
        })
        .catch((err) => {
          console.error('路由跳转失败:', err)
          ElMessage.error('页面跳转失败，请稍后重试')
        })
    } catch (err) {
      console.error('路由跳转异常:', err)
      ElMessage.error('页面跳转发生异常，请稍后重试')
    }
  } else {
    console.log('当前已在目标页面，无需跳转')
  }
}

// 切换全屏
const isFullscreen = ref(false)
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen()
      isFullscreen.value = false
    }
  }
}

// 退出登录
const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        await logout()
        userStore.clearUserInfo()
        ElMessage.success('退出登录成功')
        router.push('/')
      } catch (error) {
        console.error('退出登录失败', error)
        // 即使API调用失败，也清除本地状态
        userStore.clearUserInfo()
        router.push('/')
      }
    })
    .catch(() => {
      // 取消退出
    })
}

// 响应式处理
const isMobile = ref(false)
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
  if (isMobile.value && !isSidebarCollapsed.value) {
    isSidebarCollapsed.value = true
  }
}

// 当前语言
const currentLanguage = ref(getLanguageDisplayName(getCurrentLanguage()))

// 创建防抖函数
const debounce = (fn: Function, delay: number) => {
  let timer: any = null
  return function (this: any, ...args: any[]) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
      timer = null
    }, delay)
  }
}

// 防抖处理的语言切换函数
const debouncedChangeLanguage = debounce((lang: LanguageType) => {
  // 使用语言服务中的方法切换语言
  if (changeLanguage(lang)) {
    // 更新UI显示
    currentLanguage.value = getLanguageDisplayName(lang)
  }
}, 300)

// 处理语言切换
const handleLanguageChange = (lang: LanguageType) => {
  // 使用防抖处理的语言切换函数
  debouncedChangeLanguage(lang)
}

// 初始化翻译脚本（简化版，主要逻辑已移至语言服务）
const initTranslateScript = () => {
  // 从语言服务获取当前语言并更新UI显示
  currentLanguage.value = getLanguageDisplayName(getCurrentLanguage())

  // 监听语言变化事件
  window.addEventListener('language-changed', ((e: CustomEvent<{ language: LanguageType }>) => {
    if (e.detail && e.detail.language) {
      currentLanguage.value = getLanguageDisplayName(e.detail.language)

      // 在语言切换后恢复菜单展开状态
      nextTick(() => {
        // 确保DOM更新后再应用菜单状态
        if (openedMenus.value.length > 0) {
          applyOpenedMenus(openedMenus.value)
        } else {
          setDefaultOpenedMenus(route.path)
        }
      })
    }
  }) as EventListener)
}
// 未读消息数量
const unreadCount = ref(0)

// 自定义事件更新消息数量
const handelChangeUnreadCount=(count:number)=>{
  unreadCount.value = count
  console.log('更新未读消息数量',unreadCount.value);
}
// 商家详细信息
interface SellerDetailInfoType {
  shopName?: string
  photoUrl?: string
  email?: string
  accountName?: string
  [key: string]: any
}

const sellerDetail = ref<SellerDetailInfoType | null>(null)
const isLoadingSellerDetail = ref(false)

// 获取商家详情
const fetchSellerDetail = async () => {
  if (!userStore.userInfo?.id) {
    ElMessage.warning('未获取到用户ID，请重新登录')
    return
  }

  isLoadingSellerDetail.value = true
  try {
    const sellerId = userStore.userInfo.id
    console.log(`正在请求商家基本信息，ID: ${sellerId}，请求URL: /seller/${sellerId}`)
    const response = await getSellerById(sellerId)
    console.log('获取商家基本信息响应:', response)

    if (response.code === 1 && response.data) {
      // 保存商家基本信息
      sellerDetail.value = response.data
      console.log('设置的商家基本信息:', sellerDetail.value)

      // 更新用户信息到store
      userStore.setUserInfo({
        ...userStore.userInfo,
        accountName: response.data.accountName || userStore.userInfo.accountName,
        avatar: response.data.photoUrl || userStore.userInfo.avatar,
      })
    } else {
      ElMessage.error(response.msg || '获取商家信息失败')
    }
  } catch (error) {
    console.error('获取商家信息失败:', error)
    ElMessage.error('获取商家信息失败，请稍后重试')
  } finally {
    isLoadingSellerDetail.value = false
  }
}

// 应用已保存的菜单展开状态
const applyOpenedMenus = (menus: string[]) => {
  if (!menus || menus.length === 0) return

  openedMenus.value = menus
  // 强制更新菜单组件，确保状态生效
  nextTick(() => {
    // 确保菜单组件已经渲染
    if (sideMenu.value) {
      // 通过ref直接设置菜单组件的openKeys属性
      // 注意：根据使用的UI组件库不同，这里的实现可能需要调整
      console.log('应用菜单展开状态:', openedMenus.value)
    }
  })
}

// 监听路由变化，动态更新面包屑和活动菜单
watch(
  () => route.path,
  (newPath) => {
    // 保存当前菜单展开状态
    saveOpenedMenus()

    // 延迟更新，避免与翻译过程冲突
    setTimeout(() => {
      // 根据路径更新活动菜单和面包屑
      if (newPath.includes('/main/welcome')) {
        activeMenu.value = 'welcome'
        breadcrumbs.value = ['欢迎页面']
      } else if (newPath.includes('/main/dashboard')) {
        activeMenu.value = 'dashboard'
        breadcrumbs.value = ['控制台']
      } else if (newPath.includes('/main/product/list')) {
        activeMenu.value = 'product-list'
        breadcrumbs.value = ['商品管理', '商品列表']
      } else if (newPath.includes('/main/product/add')) {
        activeMenu.value = 'product-add'
        breadcrumbs.value = ['商品管理', '添加商品']
      } else if (newPath.includes('/main/product/audit')) {
        activeMenu.value = 'product-audit'
        breadcrumbs.value = ['商品审核']
      } else if (newPath.includes('/main/category/list')) {
        activeMenu.value = 'category-list'
        breadcrumbs.value = ['分类管理', '分类列表']
      } else if (newPath.includes('/main/seller/audit')) {
        activeMenu.value = 'seller-audit'
        breadcrumbs.value = ['商家管理', '商家审核']
      } else if (newPath.includes('/main/user-management/user-list')) {
        activeMenu.value = 'user-list'
        breadcrumbs.value = ['用户管理', '用户列表']
      } else if (newPath.includes('/main/user-management/purchase-analysis')) {
        activeMenu.value = 'purchase-analysis'
        breadcrumbs.value = ['用户管理', '购买分析']
      } else if (newPath.includes('/main/user-management/user-detail')) {
        activeMenu.value = 'user-list'
        breadcrumbs.value = ['用户管理', '用户列表', '用户详情']
      } else if (newPath.includes('/main/commission/leader-list')) {
        console.log('路由变化: 进入团长管理页面')
        activeMenu.value = 'leader-list'
        breadcrumbs.value = ['佣金模式', '团长管理']
      } else if (newPath.includes('/main/commission/add-leader')) {
        console.log('路由变化: 进入添加团长页面')
        activeMenu.value = 'add-leader'
        breadcrumbs.value = ['佣金模式', '添加团长']
      } else if (newPath.includes('/main/commission/edit-leader')) {
        console.log('路由变化: 进入编辑团长页面')
        activeMenu.value = 'leader-list'
        breadcrumbs.value = ['佣金模式', '团长管理', '编辑团长']
      } else if (newPath.includes('/main/commission/commission-settings')) {
        console.log('路由变化: 进入佣金设置页面')
        activeMenu.value = 'commission-settings'
        breadcrumbs.value = ['佣金模式', '佣金设置']
      } else if (newPath.includes('/main/commission/invitation-codes')) {
        console.log('路由变化: 进入邀请码管理页面')
        activeMenu.value = 'invitation-codes'
        breadcrumbs.value = ['佣金模式', '邀请码管理']
      } else if (newPath.includes('/main/commission/commission-statistics')) {
        console.log('路由变化: 进入佣金统计页面')
        activeMenu.value = 'commission-statistics'
        breadcrumbs.value = ['佣金模式', '佣金统计']
      } else if (newPath.includes('/main/permission/seller')) {
        activeMenu.value = 'seller-permission'
        breadcrumbs.value = ['权限管理', '商家权限']
      } else if (newPath.includes('/main/permission/rules')) {
        activeMenu.value = 'rule-management'
        breadcrumbs.value = ['权限管理', '权限规则']
      } else if (newPath.includes('/main/staff-accounts/staff-account')) {
        activeMenu.value = 'staff-account'
        breadcrumbs.value = ['子账号管理', '店铺子账号']
      } else if (newPath.includes('/main/order/list')) {
        activeMenu.value = 'order-list'
        breadcrumbs.value = ['订单管理', '订单列表']
      } else if (newPath.includes('/main/order/processing')) {
        activeMenu.value = 'order-processing'
        breadcrumbs.value = ['订单管理', '处理中订单']
      } else if (newPath.includes('/main/order/completed')) {
        activeMenu.value = 'order-completed'
        breadcrumbs.value = ['订单管理', '已完成订单']
      } else if (newPath.includes('/main/order/refund')) {
        activeMenu.value = 'order-refund'
        breadcrumbs.value = ['订单管理', '退款处理']
      } else if (newPath.includes('/main/order/statistics')) {
        activeMenu.value = 'order-statistics'
        breadcrumbs.value = ['订单统计']
      } else if (newPath.includes('/main/refund/list')) {
        activeMenu.value = 'refund-list'
        breadcrumbs.value = ['退款管理', '退款列表']
      } else if (newPath.includes('/main/refund/pending')) {
        activeMenu.value = 'refund-pending'
        breadcrumbs.value = ['退款管理', '待审核退款']
      } else if (newPath.includes('/main/tracking/list')) {
        activeMenu.value = 'tracking-list'
        breadcrumbs.value = ['物流管理', '物流列表']
      } else if (newPath.includes('/main/tracking/register')) {
        activeMenu.value = 'tracking-register'
        breadcrumbs.value = ['物流管理', '注册物流']
      }
      // ... existing code ...

      // 确保菜单状态在路由变化后保持
      if (openedMenus.value.length > 0) {
        applyOpenedMenus(openedMenus.value)
      } else {
        setDefaultOpenedMenus(newPath)
      }
    }, 300) // 增加延迟执行时间
  },
  { deep: false }, // 不进行深度监听，减少不必要的触发
)

// 帮助中心相关
const helpDialogVisible = ref(false)

const showHelpDialog = () => {
  helpDialogVisible.value = true
}

const closeHelpDialog = () => {
  helpDialogVisible.value = false
}

const goToHelpDocs = () => {
  helpDialogVisible.value = false
  const helpUrl = '/main/help/docs'

  if (helpUrl.startsWith('/')) {
    router.push(helpUrl)
  } else {
    window.open(helpUrl, '_blank')
  }
}

// 显示通知
const showNotifications = () => {
  ElNotification({
    title: '通知',
    message: '暂无新消息',
    type: 'info',
    duration: 3000,
    position: 'top-right',
  })
  hasUnreadNotifications.value = false
}

// 在组件加载时初始化
onMounted(() => {
  
  // 从本地存储恢复侧边栏状态和宽度
  const savedCollapsed = localStorage.getItem('sidebarCollapsed')
  if (savedCollapsed) {
    isSidebarCollapsed.value = savedCollapsed === 'true'
  }

  const savedWidth = localStorage.getItem('sidebarWidth')
  if (savedWidth) {
    sidebarWidth.value = parseInt(savedWidth)
  }

  // 从本地存储中恢复已展开的菜单
  const savedOpenedMenus = localStorage.getItem('openedMenus')
  if (savedOpenedMenus) {
    try {
      const parsedMenus = JSON.parse(savedOpenedMenus)
      applyOpenedMenus(parsedMenus)
    } catch (e) {
      console.error('解析已展开菜单失败:', e)
      // 解析失败时使用默认逻辑
      setDefaultOpenedMenus(route.path)
    }
  } else {
    // 根据当前路径自动展开相应的菜单
    setDefaultOpenedMenus(route.path)
  }

  // 根据当前路径更新活动菜单
  updateActiveMenuByPath(route.path)


  // 检查当前路由的权限
  checkRoutePermission(route.path)

  // 添加全局前置守卫监听路由变化
  const unwatch = router.beforeEach((to, from, next) => {
    // 在路由变化前保存当前菜单展开状态
    saveOpenedMenus()

    if (to.path.startsWith('/main/')) {
      const pathSegments = to.path.split('/')
      const module = pathSegments[2] // 例如 'permission'
      const page = pathSegments[3] // 例如 'seller'

      // 检查是否有权限访问
      if (module === 'permission') {
        if (page === 'seller' && !hasPermission('SellerPermission')) {
          ElMessage.error('您没有访问商家权限页面的权限')
          next('/main/welcome')
          return
        }

        if (page === 'rules' && !hasPermission('RuleManagement')) {
          ElMessage.error('您没有访问权限规则页面的权限')
          next('/main/welcome')
          return
        }

        if (
          !hasPermission('Permission') &&
          !hasPermission('SellerPermission') &&
          !hasPermission('RuleManagement')
        ) {
          ElMessage.error('您没有访问权限管理模块的权限')
          next('/main/welcome')
          return
        }
      }

      // 检查是否有权限访问佣金模式模块
      if (module === 'commission') {
        console.log('正在检查佣金模式权限')

        // 检查具体页面权限
        if (page === 'leader-list' && !hasPermission('LeaderList')) {
          ElMessage.error('您没有访问团长管理页面的权限')
          next('/main/welcome')
          return
        }

        if (page === 'add-leader' && !hasPermission('AddLeader')) {
          ElMessage.error('您没有访问添加团长页面的权限')
          next('/main/welcome')
          return
        }

        if (page === 'commission-settings' && !hasPermission('CommissionSettings')) {
          ElMessage.error('您没有访问佣金设置页面的权限')
          next('/main/welcome')
          return
        }

        if (page === 'invitation-codes' && !hasPermission('InvitationCodes')) {
          ElMessage.error('您没有访问邀请码管理页面的权限')
          next('/main/welcome')
          return
        }

        if (page === 'commission-statistics' && !hasPermission('CommissionStatistics')) {
          ElMessage.error('您没有访问佣金统计页面的权限')
          next('/main/welcome')
          return
        }

        // 检查是否有佣金模式总权限
        if (
          !hasPermission('Commission') &&
          !hasPermission('LeaderList') &&
          !hasPermission('AddLeader') &&
          !hasPermission('CommissionSettings') &&
          !hasPermission('InvitationCodes') &&
          !hasPermission('CommissionStatistics')
        ) {
          ElMessage.error('您没有访问佣金模式模块的权限')
          next('/main/welcome')
          return
        }
      }

      // 其他模块的权限检查可以按类似方式添加
    }
    next()
  })

  // 从本地存储加载导航历史
  const savedHistory = localStorage.getItem('navHistory')
  if (savedHistory) {
    try {
      navHistory.value = JSON.parse(savedHistory)
    } catch (e) {
      console.error('解析导航历史失败:', e)
    }
  }

  // 初始化
  checkMobile()
  window.addEventListener('resize', checkMobile)
  fetchSellerDetail()
  initTranslateScript() // 初始化翻译

  // 加载用户偏好设置
  userStore.loadPreferences()

  // 打印权限和用户信息，帮助调试
  console.log('当前用户信息:', userStore.userInfo)
  console.log('当前用户的权限信息:', userStore.permissions)
  console.log('权限检查结果 - dashboard:', hasPermission('dashboard'))
  console.log('权限检查结果 - Product:', hasPermission('Product'))

  // 在组件卸载时移除路由守卫
  onBeforeUnmount(() => {
    unwatch()
  })

  // 恢复保存的菜单状态
  try {
    const savedMenus = localStorage.getItem('openedMenus')
    if (savedMenus) {
      const parsedMenus = JSON.parse(savedMenus)
      console.log('恢复保存的菜单状态:', parsedMenus)
      applyOpenedMenus(parsedMenus)
    } else {
      // 如果没有保存的菜单状态，根据当前路由设置默认展开菜单
      const currentPath = route.path
      setDefaultOpenedMenus(currentPath)
    }
  } catch (error) {
    console.error('恢复菜单状态失败:', error)
    // 如果恢复失败，使用默认设置
    setDefaultOpenedMenus(route.path)
  }
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', checkMobile)
  document.removeEventListener('mousemove', resize)
  document.removeEventListener('mouseup', stopResizing)
  // 移除语言变化事件监听
  window.removeEventListener('language-changed', () => {})
})

// 导航栏操作
const handleCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/main/user/profile')
      break
    case 'settings':
      router.push('/main/user/settings')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// ... existing code ...
// 在主页面加载时调用
onMounted(async () => {
  // 打印权限和用户信息，帮助调试
  console.log('当前用户信息:', userStore.userInfo)
  console.log('当前用户的权限信息:', userStore.permissions)
  console.log('权限检查结果 - dashboard:', hasPermission('dashboard'))
  console.log('权限检查结果 - Product:', hasPermission('Product'))

  // ... existing code ...
})

// 检查当前路由的权限
const checkRoutePermission = (path: string) => {
  if (path.startsWith('/main/permission')) {
    const pathSegments = path.split('/')
    const page = pathSegments[3] // 例如 'seller' 或 'rules'

    if (page === 'seller' && !hasPermission('SellerPermission')) {
      ElMessage.error('您没有访问商家权限页面的权限')
      router.push('/main/welcome')
      return false
    }

    if (page === 'rules' && !hasPermission('RuleManagement')) {
      ElMessage.error('您没有访问权限规则页面的权限')
      router.push('/main/welcome')
      return false
    }

    if (
      !hasPermission('Permission') &&
      !hasPermission('SellerPermission') &&
      !hasPermission('RuleManagement')
    ) {
      ElMessage.error('您没有访问权限管理模块的权限')
      router.push('/main/welcome')
      return false
    }
  }

  // 检查佣金模式权限
  if (path.startsWith('/main/commission')) {
    const pathSegments = path.split('/')
    const page = pathSegments[3] // 例如 'leader-list' 或 'add-leader'

    // 检查具体页面权限
    if (page === 'leader-list' && !hasPermission('LeaderList')) {
      ElMessage.error('您没有访问团长管理页面的权限')
      router.push('/main/welcome')
      return false
    }

    if (page === 'add-leader' && !hasPermission('AddLeader')) {
      ElMessage.error('您没有访问添加团长页面的权限')
      router.push('/main/welcome')
      return false
    }

    if (page === 'commission-settings' && !hasPermission('CommissionSettings')) {
      ElMessage.error('您没有访问佣金设置页面的权限')
      router.push('/main/welcome')
      return false
    }

    if (page === 'invitation-codes' && !hasPermission('InvitationCodes')) {
      ElMessage.error('您没有访问邀请码管理页面的权限')
      router.push('/main/welcome')
      return false
    }

    if (page === 'commission-statistics' && !hasPermission('CommissionStatistics')) {
      ElMessage.error('您没有访问佣金统计页面的权限')
      router.push('/main/welcome')
      return false
    }

    // 检查是否有佣金模式总权限
    if (
      !hasPermission('Commission') &&
      !hasPermission('LeaderList') &&
      !hasPermission('AddLeader') &&
      !hasPermission('CommissionSettings') &&
      !hasPermission('InvitationCodes') &&
      !hasPermission('CommissionStatistics')
    ) {
      ElMessage.error('您没有访问佣金模式模块的权限')
      router.push('/main/welcome')
      return false
    }
  }

  return true
}

// 更新活动菜单
const updateActiveMenuByPath = (path: string) => {
  const pageName = getPageNameFromPath(path)
  if (pageName) {
    activeMenu.value = pageName.toLowerCase().replace(/\s+/g, '-')
    breadcrumbs.value = [pageName]
  }
}

// 根据路径自动设置默认展开的菜单
const setDefaultOpenedMenus = (path: string) => {
  const menuKeys: string[] = []

  // 根据路径确定应该展开的菜单
  if (path.includes('/main/product')) {
    menuKeys.push('products')
  } else if (path.includes('/main/category')) {
    menuKeys.push('categories')
  } else if (path.includes('/main/seller')) {
    menuKeys.push('sellers')
  } else if (path.includes('/main/user-management')) {
    menuKeys.push('user-management')
  } else if (path.includes('/main/staff-accounts')) {
    console.log('设置子账号管理菜单展开')
    menuKeys.push('staff-accounts')
  } else if (path.includes('/main/commission')) {
    console.log('设置佣金模式菜单展开')
    menuKeys.push('commission')
  } else if (path.includes('/main/permission')) {
    menuKeys.push('permission')
  } else if (path.includes('/main/message')) {
    menuKeys.push('message')
  } else if (path.includes('/main/order')) {
    menuKeys.push('orders')
  } else if (path.includes('/main/settings')) {
    menuKeys.push('settings')
  }

  console.log('设置默认展开菜单:', menuKeys)

  // 应用菜单状态
  applyOpenedMenus(menuKeys)
  // 同时保存到localStorage
  openedMenus.value = menuKeys
  saveOpenedMenus()
}

// 保存当前展开的菜单到localStorage
const saveOpenedMenus = () => {
  if (openedMenus.value && openedMenus.value.length > 0) {
    localStorage.setItem('openedMenus', JSON.stringify(openedMenus.value))
    console.log('保存菜单状态:', openedMenus.value)
  }
}

// 当菜单变化时保存状态
const handleMenuOpen = (key: string) => {
  // 确保菜单索引不重复
  if (!openedMenus.value.includes(key)) {
    openedMenus.value.push(key)
    saveOpenedMenus()
  }
}

const handleMenuClose = (key: string) => {
  const index = openedMenus.value.indexOf(key)
  if (index !== -1) {
    openedMenus.value.splice(index, 1)
    saveOpenedMenus()
  }
}
</script>

<style scoped lang="scss">
.main-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background-color: #f5f7fa;

  &.sidebar-collapsed {
    .sidebar {
      width: 64px !important; // 强制覆盖自定义宽度

      :deep(.el-menu--collapse) {
        width: 64px;

        .el-menu-item,
        .el-sub-menu__title {
          padding-left: 20px !important;

          .el-icon {
            margin-right: 0;
            margin-left: 2px;
          }
        }
      }

      .sidebar-header {
        padding: 0 12px;

        .logo {
          justify-content: center;

          .logo-img {
            margin-right: 0;
          }
        }
      }

      .resize-handle {
        display: none;
      }
    }

    .main-content {
      margin-left: 64px;

      @media screen and (max-width: 768px) {
        margin-left: 0;
      }
    }
  }
}

.sidebar {
  height: 100vh;
  background: linear-gradient(135deg, #f0f6fc 0%, #e7f0f9 100%);
  border-right: 1px solid rgba(200, 215, 235, 0.5);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.03);
  position: fixed;
  left: 0;
  top: 0;
  width: v-bind(sidebarWidth + 'px');
  transition: all 0.3s ease;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: 10;
  display: flex;
  flex-direction: column;

  /* 侧边栏滚动条样式 */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(146, 168, 209, 0.3);
    border-radius: 4px;

    &:hover {
      background-color: rgba(146, 168, 209, 0.5);
    }
  }

  .resize-handle {
    position: absolute;
    top: 0;
    right: -5px;
    width: 10px;
    height: 100%;
    cursor: ew-resize;
    z-index: 100;

    &:hover {
      background-color: rgba(114, 159, 207, 0.1);
    }

    &:active {
      background-color: rgba(114, 159, 207, 0.2);
    }
  }
}

.sidebar-header {
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(200, 215, 235, 0.5);
  background: linear-gradient(to right, #e1edfb, #d7e7f7);

  .logo {
    display: flex;
    align-items: center;

    .logo-img {
      width: 32px;
      height: 32px;
      margin-right: 10px;
    }

    h1 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      white-space: nowrap;
      background: linear-gradient(90deg, #3a7bd5, #2c5499);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .collapse-icon {
    font-size: 18px;
    cursor: pointer;
    color: #5d7290;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      color: #3a7bd5;
      background-color: rgba(58, 123, 213, 0.1);
    }
  }
}

.user-profile {
  padding: 16px;
  display: flex;
  align-items: center;
  background: linear-gradient(to bottom, #e1edfb, rgba(225, 237, 251, 0.6));
  border-bottom: 1px solid rgba(200, 215, 235, 0.5);

  .el-avatar {
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }

  .user-info {
    margin-left: 12px;
    overflow: hidden;

    h3 {
      margin: 0 0 4px 0;
      font-size: 15px;
      font-weight: 600;
      color: #2c3e50;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    p {
      margin: 0;
      font-size: 12px;
      color: #5d7290;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}

.user-profile-collapsed {
  padding: 16px 0;
  display: flex;
  justify-content: center;
  background: linear-gradient(to bottom, #e1edfb, rgba(225, 237, 251, 0.6));
  border-bottom: 1px solid rgba(200, 215, 235, 0.5);

  .el-avatar {
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }
}

.sidebar-menu {
  border-right: none !important;
  background-color: transparent !important;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(146, 168, 209, 0.3);
    border-radius: 4px;

    &:hover {
      background-color: rgba(146, 168, 209, 0.5);
    }
  }

  :deep(.el-menu-item) {
    height: 50px;
    line-height: 50px;
    color: #4a5568;
    font-size: 14px;
    transition: all 0.3s ease;
    position: relative;
    border-radius: 0 24px 24px 0;
    margin: 4px 0;
    margin-right: 12px;
    padding-left: 20px !important;

    .el-icon {
      color: #5d7290;
      transition: all 0.3s ease;
    }

    &:hover {
      background: linear-gradient(90deg, rgba(225, 236, 250, 0.8), rgba(213, 230, 249, 0.4));
      color: #3a7bd5;

      .el-icon {
        color: #3a7bd5;
        transform: scale(1.1);
      }
    }

    &.is-active {
      background: linear-gradient(90deg, rgba(213, 230, 249, 0.9), rgba(197, 222, 249, 0.5));
      color: #2c5499;
      font-weight: 500;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 4px;
        background: linear-gradient(to bottom, #3a7bd5, #2c5499);
        border-radius: 0 4px 4px 0;
      }

      .el-icon {
        color: #3a7bd5;
        transform: scale(1.1);
      }
    }
  }

  :deep(.el-sub-menu.is-active) {
    & > .el-sub-menu__title {
      color: #2c3e50;
      background-color: rgba(213, 230, 249, 0.4);

      .el-icon {
        color: #3a7bd5;
      }
    }
  }

  :deep(.el-sub-menu__title) {
    height: 50px;
    line-height: 50px;
    color: #4a5568;
    transition: all 0.3s ease;
    position: relative;
    border-radius: 0 24px 24px 0;
    margin: 4px 0;
    margin-right: 12px;
    padding-left: 20px !important;

    &:hover {
      background: linear-gradient(90deg, rgba(225, 236, 250, 0.8), rgba(213, 230, 249, 0.4));
      color: #3a7bd5;

      .el-icon {
        color: #3a7bd5;
      }
    }
  }

  :deep(.el-menu--inline) {
    background-color: rgba(237, 242, 249, 0.6);
    padding: 5px 0;
    margin: 0;

    .el-menu-item {
      height: 44px;
      line-height: 44px;
      margin: 2px 0;
      margin-right: 12px;
      border-radius: 0 20px 20px 0;
      padding-left: 48px !important;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 12%;
        width: 76%;
        height: 1px;
        background: linear-gradient(to right, transparent, rgba(200, 215, 235, 0.3), transparent);
      }

      &:last-child::after {
        display: none;
      }
    }
  }
}

.main-content {
  flex-grow: 1;
  height: 100vh;
  overflow-y: auto;
  background: linear-gradient(135deg, #f7fbff 0%, #eef6fd 100%);
  margin-left: v-bind(sidebarWidth + 'px');
  transition: all 0.3s ease;
  position: relative;
  width: calc(100% - v-bind(sidebarWidth + 'px'));
}

.top-navbar {
  position: sticky;
  top: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  height: 60px;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  z-index: 1001;

  .navbar-left {
    display: flex;
    align-items: center;

    .menu-toggle {
      font-size: 20px;
      cursor: pointer;
      margin-right: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #606266;
      padding: 8px;
      border-radius: 4px;
      transition: all 0.3s;

      &:hover {
        background-color: #f0f2f5;
        color: var(--el-color-primary);
      }
    }

    .breadcrumb-container {
      margin-right: 24px;
    }

    .merchant-name {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      background: linear-gradient(to right, #3a7bd5, #2c5499);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      padding: 0 12px;

      span {
        display: inline-block;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .navbar-right {
    display: flex;
    align-items: center;
    gap: 16px;

    .language-dropdown {
      margin-right: 8px;

      .nav-icon-btn {
        height: 36px;
        background-color: rgba(237, 242, 249, 0.7);
        border: 1px solid rgba(200, 215, 235, 0.3);
        border-radius: 18px;
        padding: 0 12px;
        transition: all 0.3s;

        &:hover {
          background-color: rgba(213, 230, 249, 0.7);
          border-color: rgba(146, 168, 209, 0.3);
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(58, 123, 213, 0.1);
        }

        .language-text {
          margin: 0 4px;
          font-size: 14px;
          color: #606266;
        }

        .arrow-icon {
          font-size: 12px;
          margin-left: 2px;
          color: #909399;
        }
      }
    }

    .nav-icon-btn {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 0 8px;
      height: 32px;
      border-radius: 4px;
      margin-right: 16px;

      &:hover {
        background-color: #f5f7fa;
      }

      .el-icon {
        font-size: 18px;
      }
    }

    .notification-badge {
      height: 100%;
      line-height: 1;
    }

    .user-dropdown-link {
      padding: 4px 8px;
      border-radius: 40px;
      cursor: pointer;
      display: flex;
      align-items: center;
      transition: all 0.3s;
      background-color: #f5f7fa;

      &:hover {
        background-color: #ebeef5;
      }

      .user-avatar {
        border: 2px solid #fff;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        background-color: var(--el-color-primary);
        color: #fff;
        font-weight: bold;
        margin-right: 8px;
      }

      .username {
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 14px;
        color: #303133;
        margin-right: 4px;
      }

      .el-icon {
        font-size: 14px;
        color: #909399;
      }
    }
  }
}

.sidebar-footer {
  position: relative;
  bottom: 0;
  width: auto;
  margin-top: auto;
  padding: 16px;
  background: linear-gradient(to bottom, rgba(237, 242, 249, 0), rgba(225, 236, 250, 0.8));
  border-top: 1px solid rgba(200, 215, 235, 0.5);
  display: flex;
  flex-direction: column;
  gap: 10px;
  box-sizing: border-box;
  backdrop-filter: blur(4px);

  .help-button {
    width: 100%;
    justify-content: flex-start;
    padding: 10px 12px;
    border-radius: 8px;
    transition: all 0.3s;
    color: #4a5568;
    background: rgba(237, 242, 249, 0.7);
    border: 1px solid rgba(200, 215, 235, 0.3);
    display: flex;
    align-items: center;

    .el-icon {
      margin-right: 10px;
      color: #4a7eb8;
      font-size: 18px;
      transition: all 0.3s;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        left: -6px;
        top: -6px;
        right: -6px;
        bottom: -6px;
        background: rgba(74, 126, 184, 0.1);
        border-radius: 50%;
        transform: scale(0.8);
        opacity: 0;
        transition: all 0.3s;
        z-index: -1;
      }
    }

    &:hover {
      background: linear-gradient(90deg, rgba(213, 230, 249, 0.7), rgba(197, 222, 249, 0.5));
      color: #3a7bd5;
      border-color: rgba(146, 168, 209, 0.3);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(58, 123, 213, 0.1);

      .el-icon {
        color: #3a7bd5;
        transform: scale(1.1);

        &::after {
          transform: scale(1);
          opacity: 1;
        }
      }
    }
  }

  .logout-button {
    width: 100%;
    justify-content: flex-start;
    padding: 10px 12px;
    border-radius: 8px;
    transition: all 0.3s;
    color: #4a5568;
    background: rgba(237, 242, 249, 0.7);
    border: 1px solid rgba(200, 215, 235, 0.3);
    display: flex;
    align-items: center;

    .el-icon {
      margin-right: 10px;
      color: #e06464;
      font-size: 18px;
      transition: all 0.3s;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        left: -6px;
        top: -6px;
        right: -6px;
        bottom: -6px;
        background: rgba(224, 100, 100, 0.1);
        border-radius: 50%;
        transform: scale(0.8);
        opacity: 0;
        transition: all 0.3s;
        z-index: -1;
      }
    }

    &:hover {
      background: linear-gradient(90deg, rgba(253, 227, 227, 0.7), rgba(253, 216, 216, 0.5));
      color: #e53e3e;
      border-color: rgba(229, 62, 62, 0.2);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(229, 62, 62, 0.1);

      .el-icon {
        color: #e53e3e;
        transform: scale(1.1);

        &::after {
          transform: scale(1);
          opacity: 1;
        }
      }
    }
  }
}

.main-container {
  &.sidebar-collapsed {
    .sidebar-footer {
      width: 64px;
      padding: 12px 8px;

      .help-button,
      .logout-button {
        padding: 10px 0;
        justify-content: center;

        .el-icon {
          margin-right: 0;
        }

        span {
          display: none;
        }
      }
    }
  }
}

// 移除底部的固定padding，改用flex布局自然排列
.sidebar-menu {
  padding-bottom: 0;
}

// 在移动端模式下调整底部按钮位置
@media screen and (max-width: 768px) {
  .sidebar-footer {
    width: 240px;
  }
}

// 自定义通知样式
:global(.custom-notification) {
  background: linear-gradient(135deg, #ffffff, #f7fbff);
  border-left: 4px solid #3a7bd5;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-radius: 8px;

  :global(.el-notification__title) {
    color: #2c3e50;
    font-weight: 600;
  }

  :global(.el-notification__content) {
    color: #4a5568;
  }

  :global(.el-icon) {
    color: #3a7bd5;
  }
}

// 动画
.fade-enter-active,
.fade-leave-active {
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

// 路由切换动画
:deep(.router-view-transition) {
  transition: all 0.3s ease;
}

:deep(.router-view-transition-enter-from),
:deep(.router-view-transition-leave-to) {
  opacity: 0;
  transform: translateX(20px);
}

// 侧边栏展开收起动画
.sidebar-menu {
  :deep(.el-menu-collapse-transition) {
    transition: width 0.3s cubic-bezier(0.55, 0, 0.1, 1) !important;
  }

  :deep(.el-menu--collapse) {
    .el-tooltip__trigger {
      display: flex;
      justify-content: center;

      .el-icon {
        margin: 0;
      }
    }
  }
}

// 扩展移动端样式适配
@media screen and (max-width: 768px) {
  .main-container {
    .sidebar {
      position: fixed;
      z-index: 1001;
      top: 0;
      left: 0;
      transform: translateX(-100%);
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
      width: 240px; // 移动端始终展示完整宽度
    }

    &.sidebar-collapsed {
      .sidebar {
        transform: translateX(0);
      }
    }
  }

  .main-content {
    margin-left: 0;
  }

  .top-navbar {
    padding: 0 12px;

    .breadcrumb {
      :deep(.el-breadcrumb__item) {
        &:not(:last-child) {
          display: none;
        }
      }
    }

    .top-navbar-right {
      .user-dropdown-button {
        padding: 6px;

        span {
          display: none;
        }
      }
    }
  }

  .page-content {
    padding: 12px;
  }
}

// 历史记录样式
.history-item {
  display: flex;
  align-items: center;
  font-size: 13px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  &::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #4c84ff;
    border-radius: 50%;
    margin-right: 6px;
    flex-shrink: 0;
  }
}

:deep(.el-dropdown-menu) {
  padding: 8px 0;
  min-width: 180px;
  max-height: 400px;
  overflow-y: auto;

  .el-dropdown-menu__item {
    padding: 8px 16px;
    font-size: 13px;
    line-height: 1.5;

    &:hover {
      background-color: rgba(76, 132, 255, 0.1);
    }

    .el-icon {
      margin-right: 6px;
      font-size: 16px;
    }
  }

  &::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(144, 147, 153, 0.3);
    border-radius: 2px;
  }
}

// 帮助中心对话框样式
.help-dialog {
  :deep(.el-dialog__header) {
    padding: 20px;
    margin-right: 0;
    border-bottom: 1px solid #f0f0f0;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  :deep(.el-dialog__body) {
    padding: 20px;
  }

  .help-content {
    max-height: 400px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(144, 147, 153, 0.3);
      border-radius: 2px;
    }

    .help-section {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      h3 {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 12px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;
      }

      ul {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
          display: flex;
          align-items: flex-start;
          margin-bottom: 10px;

          .el-icon {
            margin-right: 8px;
            font-size: 16px;
            color: #4c84ff;
            flex-shrink: 0;
            margin-top: 2px;
          }

          span {
            line-height: 1.5;
          }
        }
      }

      p {
        display: flex;
        align-items: center;
        margin: 10px 0;

        .el-icon {
          margin-right: 8px;
          font-size: 16px;
          color: #4c84ff;
          flex-shrink: 0;
        }
      }
    }
  }

  .dialog-footer {
    padding-top: 10px;
    text-align: right;
  }
}

.user-dropdown-button {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #334155;
  padding: 6px 12px;
  border-radius: 8px;
  transition: all 0.25s ease;
  background: rgba(237, 242, 249, 0.7);
  border: 1px solid rgba(200, 215, 235, 0.3);

  &:hover {
    background: linear-gradient(90deg, rgba(213, 230, 249, 0.7), rgba(197, 222, 249, 0.5));
    color: #3a7bd5;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(58, 123, 213, 0.1);
  }

  span {
    margin-right: 4px;
  }

  .el-icon {
    margin-left: 4px;
    transition: transform 0.2s ease;
  }

  &:hover .el-icon {
    transform: translateY(2px);
    color: #3a7bd5;
  }
}

.top-navbar-right {
  display: flex;
  align-items: center;
  gap: 12px;

  .user-dropdown {
    cursor: pointer;
    margin-right: 5px;
  }

  .user-info-display {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0 8px;
    height: 40px;
    border-radius: 20px;
    transition: all 0.3s;

    &:hover {
      background-color: #f5f7fa;
    }

    .username {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      margin-right: 4px;
    }

    .el-icon {
      font-size: 12px;
      color: #909399;
    }
  }

  .notification-badge {
    margin-right: 5px;
  }
}

:deep(.el-dropdown-menu) {
  padding: 8px 0;

  .language-item {
    display: flex;
    align-items: center;
    padding: 0 4px;

    .active-language {
      color: var(--el-color-primary);
      font-weight: 500;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        left: -12px;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background-color: var(--el-color-primary);
      }
    }
  }
}
// 站内信徽章
.message-popover {
  padding: 0 !important;
  max-height: 60vh;
  overflow-y: auto;

  .el-tabs__header {
    margin: 0;
    padding: 0 12px;
  }
}
</style>
